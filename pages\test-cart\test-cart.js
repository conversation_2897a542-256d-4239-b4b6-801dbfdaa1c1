const cartManager = require('../../utils/cartManager');

Page({
  data: {
    cartInfo: {
      cartCount: 0,
      cartItemCount: 0,
      selectedCount: 0,
      totalPrice: 0,
      cartItems: []
    },
    testProduct: {
      id: 'test-001',
      goodsName: '测试商品',
      wholesalePrice: 99.99,
      masterImg: '/static/images/test-product.jpg'
    }
  },

  onLoad() {
    this.updateCartInfo();
    
    // 注册购物车数据变化监听器
    cartManager.addPageListener('test-cart', (data) => {
      this.setData({
        cartInfo: data
      });
    });
  },

  onUnload() {
    // 移除监听器
    cartManager.removePageListener('test-cart');
  },

  updateCartInfo() {
    this.setData({
      cartInfo: {
        cartCount: cartManager.getCartCount(),
        cartItemCount: cartManager.getCartItemCount(),
        selectedCount: cartManager.getSelectedCount(),
        totalPrice: cartManager.getSelectedTotalPrice(),
        cartItems: cartManager.getCartItems()
      }
    });
  },

  // 测试添加商品到购物车
  testAddToCart() {
    const result = cartManager.addToCart(this.data.testProduct, 1);
    wx.showToast({
      title: result.message,
      icon: result.success ? 'success' : 'error'
    });
  },

  // 测试更新商品数量
  testUpdateQuantity() {
    const items = cartManager.getCartItems();
    if (items.length > 0) {
      const firstItem = items[0];
      const newQuantity = firstItem.num + 1;
      const result = cartManager.updateQuantity(firstItem.id, newQuantity);
      wx.showToast({
        title: result.message,
        icon: result.success ? 'success' : 'error'
      });
    } else {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
    }
  },

  // 测试删除商品
  testRemoveItem() {
    const items = cartManager.getCartItems();
    if (items.length > 0) {
      const firstItem = items[0];
      const result = cartManager.removeFromCart(firstItem.id);
      wx.showToast({
        title: result.message,
        icon: result.success ? 'success' : 'error'
      });
    } else {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
    }
  },

  // 测试全选
  testSelectAll() {
    const result = cartManager.toggleSelectAll(true);
    wx.showToast({
      title: '全选成功',
      icon: 'success'
    });
  },

  // 测试取消全选
  testUnselectAll() {
    const result = cartManager.toggleSelectAll(false);
    wx.showToast({
      title: '取消全选成功',
      icon: 'success'
    });
  },

  // 测试清空购物车
  testClearCart() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          const result = cartManager.clearCart();
          wx.showToast({
            title: result.message,
            icon: result.success ? 'success' : 'error'
          });
        }
      }
    });
  },

  // 跳转到购物车页面
  goToCart() {
    wx.switchTab({
      url: '/pages/tabbar/cart/index'
    });
  }
});

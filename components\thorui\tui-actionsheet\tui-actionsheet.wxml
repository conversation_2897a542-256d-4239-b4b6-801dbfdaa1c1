<view catchtouchmove>
  <view class="tui-actionsheet {{show?'tui-actionsheet-show':''}} {{radius?'tui-actionsheet-radius':''}}">
    <view class="tui-actionsheet-tips" style="font-size:{{size}}rpx;color:{{color}}" wx:if="{{tips}}">
      {{tips}}
    </view>
    <view class="{{isCancel?'tui-operate-box':''}}">
      <block wx:for="{{itemList}}" wx:key="index">
        <view class="tui-actionsheet-btn tui-actionsheet-divider {{!isCancel && index==itemList.length-1?'tui-btn-last':''}}" hover-class="tui-actionsheet-hover" hover-stay-time="150" data-index="{{index}}" style="color:{{item.color || '#2B2B2B'}}" bindtap="handleClickItem">{{item.text}}</view>
      </block>
    </view>
    <view class="tui-actionsheet-btn tui-actionsheet-cancel" hover-class="tui-actionsheet-hover" hover-stay-time="150" wx:if="{{isCancel}}" bindtap="handleClickCancel">取消</view>
  </view>
  <view class="tui-actionsheet-mask {{show?'tui-mask-show':''}}" bindtap="handleClickMask"></view>
</view>
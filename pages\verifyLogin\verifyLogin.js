import tui from '../../common/httpRequest'
Page({
  api:{
    signin:"/pet/customer/signIn/"+tui.tenantId
  },
  data: {
    storeName: '',
    contacts: '',
    phoneNumber: '',
    password: ''
  },
  signIn(){
    console.log(this.data)
    tui.request(this.api.signin,"POST",this.data,false,false,false)
    .then(res=>{
      if(res.code == 500){
        tui.toast(res.msg)
      }else if(res.code == 200){
        wx.showToast({
          title: "注册成功,等待审核",
          icon: 'success' ,
          duration: 3000,
        })
      }
    })
  }
})
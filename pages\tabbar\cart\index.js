import tui from '../../../common/httpRequest'
Page({
  data: {
    ossUrl:tui.ossUrl(),
    dataList: [],
    isAll: false,
    totalPrice: 0,
    buyNum: 0,
    cartIds: [], //购物车id
    actions: [
      {
        name: '收藏',
        width: 64,
        color: '#fff',
        fontsize: 28,
        background: '#FFC600'
      },
      {
        name: '看相似',
        color: '#fff',
        fontsize: 28,
        width: 64,
        background: '#FF7035'
      },
      {
        name: '删除',
        color: '#fff',
        fontsize: 28,
        width: 64,
        background: '#F82400'
      }
    ],
    actions2: [],
    isEdit: false,
    productList: [],
    pageIndex: 1,
    loadding: false,
    pullUpOn: true
  },
  onShow(){
    let cartsInfo = wx.getStorageSync('carts_info');
    if(cartsInfo != undefined && cartsInfo != ""){
      this.setData({
        dataList: cartsInfo
      })
    }
    console.log(this.data.dataList)
  },
  calcHandle() {
    let buyNum = 0;
    let totalPrice = 0;
    let selectedNum = 0;
    this.data.dataList.map((item) => {
      if (item.selected) {
        buyNum += item.buyNum;
        totalPrice += item.price * item.buyNum;
        selectedNum++
      }
    })
    this.setData({
      isAll:selectedNum === this.data.dataList.length,
      buyNum:buyNum,
      totalPrice:totalPrice
    })
  },
  changeNum: function (e) {
    this.setData({
      [`dataList[${e.detail.index}].buyNum`]: e.detail.value
    },()=>{
      this.calcHandle()
    })
  },
  handlerButton: function (e) {
    let index = e.detail.index;
    let item = e.detail.item;
    tui.toast(`商品id：${item.id}，按钮index：${index}`);
  },
  editGoods: function () {
    this.setData({
      isEdit: !this.data.isEdit
    })
  },
  detail: function () {
    wx.navigateTo({
      url: '/pages/index/productDetail/productDetail'
    })
  },
  btnPay() {
    wx.navigateTo({
      url: '/pages/submitOrder/submitOrder'
    })
  },
  buyChange(e) {
    this.setData({
      cartIds: e.detail.value
    }, () => {
      let data = this.data.dataList
      data.map(item => {
        //如果购物车id为数字统一转成字符串
        if (~this.data.cartIds.indexOf(item.id)) {
          item.selected = true;
        } else {
          item.selected = false;
        }
      })
      this.setData({
        dataList: data
      }, () => {
        this.calcHandle()
      })
    })
  },
  checkAll(e) {
    this.setData({
      isAll: !this.data.isAll
    }, () => {
      let buyNum = 0;
      let totalPrice = 0;
      let data= this.data.dataList
      data.map((item) => {
        item.selected = this.data.isAll;
        if (this.data.isAll) {
          buyNum += item.num;
          totalPrice += item.wholesalePrice * item.num;
        }
      })
      this.setData({
        dataList:data,
        totalPrice: totalPrice,
        buyNum: buyNum
      })
    })
  },
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 200)
  },
  onPullDownRefresh: function () {
    let loadData = JSON.parse(JSON.stringify(this.data.productList));
    loadData = loadData.splice(0, 10)
    this.setData({
      productList: loadData,
      pageIndex: 1,
      pullUpOn: true,
      loadding: false
    })
    wx.stopPullDownRefresh()
  },
  onReachBottom: function () {
    if (!this.data.pullUpOn) return;
    this.setData({
      loadding: true
    })
    if (this.data.pageIndex == 4) {
      this.setData({
        loadding: false,
        pullUpOn: false
      })
    } else {
      let loadData = JSON.parse(JSON.stringify(this.data.productList));
      loadData = loadData.splice(0, 10)
      if (this.data.pageIndex == 1) {
        loadData = loadData.reverse();
      }
      this.setData({
        pageIndex: this.data.pageIndex + 1,
        loadding: false,
        productList: this.data.productList.concat(loadData)
      })
    }
  }
})
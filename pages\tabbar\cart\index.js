import tui from '../../../common/httpRequest'
const cartManager = require('../../../utils/cartManager');
Page({
  data: {
    ossUrl:tui.ossUrl(),
    dataList: [],
    isAll: false,
    totalPrice: 0,
    buyNum: 0,
    cartIds: [], //购物车id
    actions: [
      {
        name: '收藏',
        width: 64,
        color: '#fff',
        fontsize: 28,
        background: '#FFC600'
      },
      {
        name: '看相似',
        color: '#fff',
        fontsize: 28,
        width: 64,
        background: '#FF7035'
      },
      {
        name: '删除',
        color: '#fff',
        fontsize: 28,
        width: 64,
        background: '#F82400'
      }
    ],
    actions2: [],
    isEdit: false,
    productList: [],
    pageIndex: 1,
    loadding: false,
    pullUpOn: true
  },
  onShow(){
    // 使用购物车管理器获取数据
    const cartItems = cartManager.getCartItems();
    this.setData({
      dataList: cartItems
    }, () => {
      this.calcHandle();
    });

    // 注册购物车数据变化监听器
    cartManager.addPageListener('cart-page', (data) => {
      this.setData({
        dataList: data.cartItems
      }, () => {
        this.calcHandle();
      });
    });
  },

  onHide: function() {
    // 页面隐藏时移除监听器
    cartManager.removePageListener('cart-page');
  },

  onUnload: function() {
    // 页面卸载时移除监听器
    cartManager.removePageListener('cart-page');
  },
  calcHandle() {
    // 使用购物车管理器计算数据
    const selectedItems = cartManager.getSelectedItems();
    const selectedCount = cartManager.getSelectedCount();
    const totalPrice = cartManager.getSelectedTotalPrice();
    const isAllSelected = cartManager.isAllSelected();

    this.setData({
      isAll: isAllSelected,
      buyNum: selectedCount,
      totalPrice: totalPrice
    })
  },
  changeNum: function (e) {
    const index = e.detail.index;
    const newQuantity = e.detail.value;
    const item = this.data.dataList[index];

    if (item) {
      // 使用购物车管理器更新数量
      const result = cartManager.updateQuantity(item.id, newQuantity);
      if (result.success) {
        // 更新本地数据
        this.setData({
          [`dataList[${index}].num`]: newQuantity
        }, () => {
          this.calcHandle();
        });
      }
    }
  },
  handlerButton: function (e) {
    let index = e.detail.index;
    let item = e.detail.item;

    if (index === 0) {
      // 收藏功能
      tui.toast('收藏功能开发中~');
    } else if (index === 1) {
      // 看相似功能
      tui.toast('看相似功能开发中~');
    } else if (index === 2) {
      // 删除商品
      this.removeItem(item);
    }
  },

  // 删除单个商品
  removeItem: function(item) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          const result = cartManager.removeFromCart(item.id);
          if (result.success) {
            // 重新获取购物车数据
            this.setData({
              dataList: cartManager.getCartItems()
            }, () => {
              this.calcHandle();
            });
            tui.toast(result.message);
          }
        }
      }
    });
  },
  editGoods: function () {
    this.setData({
      isEdit: !this.data.isEdit
    })
  },

  // 清空购物车
  clearCart: function() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          const result = cartManager.clearCart();
          if (result.success) {
            this.setData({
              dataList: [],
              isAll: false,
              totalPrice: 0,
              buyNum: 0
            });
            tui.toast(result.message);
          }
        }
      }
    });
  },

  // 删除选中的商品
  deleteSelected: function() {
    const selectedItems = cartManager.getSelectedItems();
    if (selectedItems.length === 0) {
      tui.toast('请先选择要删除的商品');
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}个商品吗？`,
      success: (res) => {
        if (res.confirm) {
          const result = cartManager.removeSelectedItems();
          if (result.success) {
            this.setData({
              dataList: cartManager.getCartItems(),
              isAll: false,
              totalPrice: result.totalPrice,
              buyNum: 0
            });
            tui.toast(result.message);
          }
        }
      }
    });
  },
  detail: function () {
    wx.navigateTo({
      url: '/pages/index/productDetail/productDetail'
    })
  },
  btnPay() {
    const selectedItems = cartManager.getSelectedItems();

    if (selectedItems.length === 0) {
      tui.toast('请先选择要结算的商品');
      return;
    }

    wx.navigateTo({
      url: '/pages/submitOrder/submitOrder'
    });
  },
  buyChange(e) {
    const selectedIds = e.detail.value;
    this.setData({
      cartIds: selectedIds
    }, () => {
      // 使用购物车管理器更新选中状态
      this.data.dataList.forEach(item => {
        const isSelected = selectedIds.includes(String(item.id));
        cartManager.toggleItemSelection(item.id, isSelected);
        item.selected = isSelected;
      });

      this.setData({
        dataList: cartManager.getCartItems()
      }, () => {
        this.calcHandle();
      });
    });
  },
  checkAll(e) {
    const newSelectAll = !this.data.isAll;

    // 使用购物车管理器全选/取消全选
    const result = cartManager.toggleSelectAll(newSelectAll);

    if (result.success) {
      this.setData({
        isAll: result.isAllSelected,
        dataList: cartManager.getCartItems(),
        totalPrice: result.totalPrice,
        buyNum: result.selectedCount
      });
    }
  },
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 200)
  },
  onPullDownRefresh: function () {
    let loadData = JSON.parse(JSON.stringify(this.data.productList));
    loadData = loadData.splice(0, 10)
    this.setData({
      productList: loadData,
      pageIndex: 1,
      pullUpOn: true,
      loadding: false
    })
    wx.stopPullDownRefresh()
  },
  onReachBottom: function () {
    if (!this.data.pullUpOn) return;
    this.setData({
      loadding: true
    })
    if (this.data.pageIndex == 4) {
      this.setData({
        loadding: false,
        pullUpOn: false
      })
    } else {
      let loadData = JSON.parse(JSON.stringify(this.data.productList));
      loadData = loadData.splice(0, 10)
      if (this.data.pageIndex == 1) {
        loadData = loadData.reverse();
      }
      this.setData({
        pageIndex: this.data.pageIndex + 1,
        loadding: false,
        productList: this.data.productList.concat(loadData)
      })
    }
  }
})
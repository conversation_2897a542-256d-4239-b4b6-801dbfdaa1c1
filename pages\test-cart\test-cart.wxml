<view class="container">
  <view class="header">
    <text class="title">购物车功能测试</text>
  </view>

  <!-- 购物车信息显示 -->
  <view class="cart-info">
    <view class="info-item">
      <text class="label">商品种类数：</text>
      <text class="value">{{cartInfo.cartItemCount}}</text>
    </view>
    <view class="info-item">
      <text class="label">商品总数量：</text>
      <text class="value">{{cartInfo.cartCount}}</text>
    </view>
    <view class="info-item">
      <text class="label">选中商品数量：</text>
      <text class="value">{{cartInfo.selectedCount}}</text>
    </view>
    <view class="info-item">
      <text class="label">选中商品总价：</text>
      <text class="value price">￥{{cartInfo.totalPrice.toFixed(2)}}</text>
    </view>
  </view>

  <!-- 测试按钮 -->
  <view class="test-buttons">
    <button class="test-btn add-btn" bindtap="testAddToCart">添加测试商品</button>
    <button class="test-btn update-btn" bindtap="testUpdateQuantity">增加商品数量</button>
    <button class="test-btn remove-btn" bindtap="testRemoveItem">删除第一个商品</button>
    <button class="test-btn select-btn" bindtap="testSelectAll">全选商品</button>
    <button class="test-btn unselect-btn" bindtap="testUnselectAll">取消全选</button>
    <button class="test-btn clear-btn" bindtap="testClearCart">清空购物车</button>
    <button class="test-btn cart-btn" bindtap="goToCart">查看购物车</button>
  </view>

  <!-- 购物车商品列表 -->
  <view class="cart-items" wx:if="{{cartInfo.cartItems.length > 0}}">
    <view class="items-header">
      <text class="items-title">购物车商品列表</text>
    </view>
    <view class="item" wx:for="{{cartInfo.cartItems}}" wx:key="id">
      <view class="item-info">
        <text class="item-name">{{item.goodsName}}</text>
        <text class="item-price">￥{{item.wholesalePrice}}</text>
        <text class="item-quantity">数量: {{item.num}}</text>
        <text class="item-selected">{{item.selected ? '已选中' : '未选中'}}</text>
      </view>
    </view>
  </view>

  <view class="empty-cart" wx:else>
    <text class="empty-text">购物车为空</text>
  </view>
</view>

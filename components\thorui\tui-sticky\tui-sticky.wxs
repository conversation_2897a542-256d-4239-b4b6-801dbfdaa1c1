var stickyChange = function (scrollTop, oldScrollTop, ownerInstance, ins) {
  if(!oldScrollTop && scrollTop === 0) return false;
  var dataset = ins.getDataset()
  var top = dataset.top;
  var height = dataset.height;
  var stickyTop = dataset.stickytop
  var isFixed = false;
  if (dataset.container) {
    isFixed = (scrollTop + stickyTop >= top && scrollTop + stickyTop < top + height) ? true : false
  } else {
    isFixed = scrollTop + stickyTop >= top ? true : false
  }
  if (isFixed) {
    ownerInstance.selectComponent('.tui-sticky-bar').setStyle({
      "top": stickyTop + 'px'
    }).addClass('tui-sticky-fixed')
    ownerInstance.selectComponent('.tui-sticky-seat').setStyle({
      "display": 'block'
    })
  } else {
    ownerInstance.selectComponent('.tui-sticky-bar').setStyle({
      "top": 'auto'
    }).removeClass('tui-sticky-fixed')
    ownerInstance.selectComponent('.tui-sticky-seat').setStyle({
      "display": 'none'
    })
  }
  //通知是否吸顶
  ownerInstance.triggerEvent("sticky", {
    isFixed: isFixed,
    index: dataset.index
  })
}

module.exports = {
  stickyChange: stickyChange
}
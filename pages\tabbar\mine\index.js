import tui from '../../../common/httpRequest'
Page({
  data: {
    isLogin: false,
    nickname: wx.getStorageSync('nickname'),
    webURL: "https://www.thorui.cn/wx",
    top: 0, //标题图标距离顶部距离
    opcity: 0,
    scrollTop: 0.5,
    productList: [],
    pageIndex: 1,
    loadding: false,
    pullUpOn: true
  },
  href(e) {
    let page = Number(e.currentTarget.dataset.page)
    let url = "";
    switch (page) {
      case 2:
        url = "/pages/my/set/set"
        break;
      case 3:
        url = "/pages/my/userInfo/userInfo"
        break;
      case 4:
        url = "/pages/my/myOrder/myOrder"
        break;
      case 6:
        url = "/pages/index/coupon/coupon"
        break;
      case 7:
        url = "/pages/my/message/message"
        break;
      case 8:
        url = "/pages/my/myCoupon/myCoupon"
        break;
      case 9:
        url = '/pages/my/myGroup/myGroup';
        break;
      case 10:
        url = '/pages/my/refundList/refundList';
        break;
      case 11:
        url = '/pages/my/wallet/wallet';
        break;
      default:
        break;
    }
    if (url) {
      if (page == 3 && !this.data.isLogin) {
        this.login()
      }else{
        tui.href(url);
      }
    } else {
      tui.toast("功能尚未完善~")
    }
  },
  detail: function () {
    wx.navigateTo({
      url: '/pages/index/productDetail/productDetail'
    })
  },
  initNavigation(e) {
    this.setData({
      opcity: e.detail.opacity,
      top: e.detail.top
    })
  },
  opcityChange(e) {
    this.setData({
      opcity: e.detail.opacity
    })
  },
  login() {
    this.setData({
      isLogin:true
    })
    tui.toast('模拟登录成功~')
  },
  onPageScroll(e) {
    this.setData({
      scrollTop: e.scrollTop
    })
  },
  onPullDownRefresh() {
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 200)
  },
  onReachBottom: function () {
    if (!this.data.pullUpOn) return;
    this.setData({
      loadding: true
    })
    if (this.data.pageIndex == 4) {
      this.setData({
        loadding: false,
        pullUpOn: false
      })
    } else {
      let loadData = JSON.parse(JSON.stringify(this.data.productList));
      loadData = loadData.splice(0, 10)
      if (this.data.pageIndex == 1) {
        loadData = loadData.reverse();
      }
      this.setData({
        productList: this.data.productList.concat(loadData),
        pageIndex: this.data.pageIndex + 1,
        loadding: false
      })
    }
  }
})
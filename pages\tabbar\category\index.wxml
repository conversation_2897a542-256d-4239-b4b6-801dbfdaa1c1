<view class="container">
  <tui-navigation-bar bindinit="initNavigation" bindchange="opcityChange" scrollTop="{{scrollTop}}" title="DIANJING PET" backgroundColor="#333" backdropFilter="{{!isAndroid}}" zIndex="999" maxOpacity="{{isAndroid ? 1 : 0.7}}" color="#fff">
  </tui-navigation-bar>
  <swiper class="tui-banner" circular autoplay>
    <swiper-item>
      <image src="https://oss.starbounce.cn/banner/23234234234.jpg" mode="aspectFill" class="tui-banner" ></image>
    </swiper-item>
    <swiper-item>
      <image src="https://oss.starbounce.cn/banner/4564fdghfjt56735.jpg"  mode="aspectFill" class="tui-banner" ></image>
    </swiper-item>
    <swiper-item>
      <image src="https://oss.starbounce.cn/banner/465890802436563588365.jpg"  mode="aspectFill" class="tui-banner" ></image>
    </swiper-item>
  </swiper>
  <view class="tui-menu__box {{total > 0?'tui-pbm__100':''}}">
    <view class="tui-left__box" wx:if="{{scrollTop + height >= menuTop && isAndroid}}"></view>
    <view class="tui-left__box {{scrollTop + height >= menuTop && isAndroid?'tui-left__fixed':''}} {{!isAndroid?'tui-left__sticky':''}}" style="height: {{windowHeight - height}}px; top: {{height}}px">
      <scroll-view scroll-y style="height: {{windowHeight - height}}px" scroll-into-view="{{scrollView_leftId}}">
        <view id="left_{{index}}" class="tui-menu__item {{ activeTab == index?'tui-menu__active':''}} {{index == activeTab - 1?'tui-bottom__radius':''}} {{index == activeTab + 1?'tui-top__radius':''}}" wx:for="{{list}}" wx:key="index" catchtap="swichNav" data-index="{{index}}">
          <image wx:if="{{index == 0}}" class="tui-menu__icon" src="{{webURL}}icon_hot_3x.png"></image>
          <text>{{ item }}</text>
        </view>
      </scroll-view>
    </view>
    <view class="tui-right__box">
      <block wx:for="{{list}}" wx:key="index">
        <t-linkage index="{{index}}" bindlinkage="linkage" scrollTop="{{scrollTop}}" recalc="{{recalc}}">
          <view class="tui-rec__list" wx:if="{{index == 0}}">
            <view class="tui-title tui-rec">推荐</view>
            <view class="tui-img__box" wx:for="{{recommends}}" wx:for-item="rec" wx:for-index="idx" wx:key="index">
              <image src="https://oss.starbounce.cn/{{rec.recommendImage}}" class="tui-rec__img"></image>
              <view class="tui-goods__box">
                <view class="tui-rec__title">{{rec.goodsName}}</view>
                <view class="tui-rec__attr">{{rec.recommend}}</view>
                <view class="tui-rec__price">￥{{rec.wholesalePrice}}</view>
                <tui-button type="brown" width="164rpx" height="56rpx" size="26" shape="circle" bindclick="toGoodsDetails" data-type="{{rec.goodsId}}">查看</tui-button>
              </view>
            </view>
          </view>
          <view class="tui-goods__list" wx:else>
            <view class="tui-title">{{ item }}</view>
            <tui-list-cell wx:for="{{ goodsList[item] }}" wx:key="index" wx:for-item="goods" hover="{{false}}" padding="30rpx">
              <view class="tui-goods__item" >
                <image src="{{ossUrl + goods.masterImg}}" data-type="{{goods.id}}" bindtap="toGoodsDetails"  class="tui-goods__img"></image>
                <view class="tui-goods__info" data-type="{{goods.id}}" bindtap="toGoodsDetails">
                  <view class="tui-goods__title">{{goods.goodsName}}</view>
                  <view>￥{{goods.wholesalePrice}}</view>
                </view>
                <image src="/static/images/cafe/plus_circle.png" data-type="{{goods.id}}" class="tui-plus__img" bindtap="getGoodsInfo"></image>
              </view>
            </tui-list-cell>
          </view>
        </t-linkage>
      </block>
    </view>
  </view>
  <!--========点单弹层 start========-->
  <tui-modal show="{{modal}}" bindcancel="hideModel" custom padding="0" radius="8rpx" width="88%">
    <view class="tui-order__modal">
      <image src="{{ossUrl + goodsInfo.masterImg}}" class="tui-img__detail" mode="widthFix"></image>
      <view class="tui-modal__box">
        <view class="tui-modal__center">
          <view class="tui-title__goods">{{goodsInfo.goodsName}}</view>
          <view style="font-size: 12px;color: #888888; font-weight: 300;">{{goodsInfo.instruction}}</view>
        </view>
        <view class="tui-modal__bottom">
          <view class="tui-price__box">
            <view class="tui-price">￥{{goodsInfo.wholesalePrice}} <text style="margin-left: 2px; font-size: 12px;color: #888888; font-weight: 400;">建议零售价￥{{goodsInfo.retailPrice}}</text></view>
            <view class="tui-number__box">
              <image src="/static/images/cafe/minus_circle.png" class="tui-plus__img {{num == 1?'tui-disabled':''}}" catchtap="reducess"></image>
              <view class="tui-num">{{ num }}</view>
              <image src="/static/images/cafe/plus_circle.png" class="tui-plus__img" catchtap="add"></image>
            </view>
          </view>
          <view class="tui-btn__box">
            <view class="tui-attr__selected">
              <view class="tui-name">规格</view>
              <view class="tui-sku">{{goodsInfo.specification}}</view>
            </view>
            <tui-button type="brown" width="180rpx" height="60rpx" size="26" shape="circle" bindclick="addCart" data-type="2">加入购物车</tui-button>
          </view>
        </view>
        <view class="tui-cancel__icon" catchtap="hideModel">
          <tui-icon name="shut" color="#f5f5f5" size="40" unit="rpx" bold></tui-icon>
        </view>
      </view>
    </view>
  </tui-modal>
  <!--========点单弹层 end========-->

  <!--========购物车弹层 start========-->
  <tui-bottom-popup translateY="-98rpx" show="{{show}}" bindclose="popupHide">
    <view class="tui-popup__box">
      <view class="tui-popup__header">
        <view class="tui-empty__box">
          <tui-icon name="delete" size="28" unit="rpx"></tui-icon>
          <text class="tui-empty__text">清空购物袋</text>
        </view>
        <tui-icon name="shut" size="44" unit="rpx" bindclick="popupHide"></tui-icon>
      </view>
      <scroll-view class="tui-cart__list" scroll-y>
        <block wx:for="{{3}}" wx:key="index">
          <tui-list-cell hover="{{false}}" padding="40rpx 30rpx">
            <view class="tui-cart__item">

              <view class="tui-number__box">
                <image src="/static/images/cafe/minus_circle.png" class="tui-plus__img"></image>
                <view class="tui-num">1</view>
                <image src="/static/images/cafe/plus_circle.png" class="tui-plus__img"></image>
              </view>
            </view>
          </tui-list-cell>
        </block>
      </scroll-view>
    </view>
  </tui-bottom-popup>
  <!--========购物车弹层 end========-->

  <!--========已点单bar start========-->
  <view class="tui-order__bar" wx:if="{{total > 0}}">
    <view class="tui-bags__box" bindtap="popupShow">
      <image src="/static/images/cafe/shopping_bag.png" class="tui-bags__img"></image>
      <tui-badge absolute type="red" scaleRatio="0.9" translateX="50%">{{ total }}</tui-badge>
    </view>
    <view class="tui-total__price">合计：￥93.00</view>
    <view class="tui-submit">
      <tui-button type="brown" width="164rpx" height="56rpx" size="26" shape="circle" bindclick="submitOrder">下单</tui-button>
    </view>
  </view>
</view>

<wxs module="parse">
  module.exports = {
    getPrice: function (price) {
      price = price || 0
      return price.toFixed(2)
    }
  }
</wxs>
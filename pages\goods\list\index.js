import tui from '../../../common/httpRequest'
Page({
  api:{
    goodsList: "/pet/goods/list",
  },
      
  data: {
    ossUrl:tui.ossUrl(),
    searchKey: '', //搜索关键词
    width: 200, //header宽度
    height: 64, //header高度
    inputTop: 0, //搜索框距离顶部距离
    arrowTop: 0, //箭头距离顶部距离
    dropScreenH: 0, //下拉筛选框距顶部距离
    attrData: [],
    attrIndex: -1,
    dropScreenShow: false,
    scrollTop: 0,
    tabIndex: 0, //顶部筛选索引
    isList: false, //是否以列表展示  | 列表或大图
    drawer: false,
    drawerH: 0, //抽屉内部scrollview高度
    selectedName: '综合',
    selectH: 0,
    dropdownList: [{
        name: '综合',
        selected: true
      },
      {
        name: '价格升序',
        selected: false
      },
      {
        name: '价格降序',
        selected: false
      }
    ],
    attrArr: [
    ],
    productList: [],
    pageIndex: 1,
    loadding: false,
    pullUpOn: true
  },
  onLoad: function (options) {
    let obj = wx.getMenuButtonBoundingClientRect();
    this.setData({
      width: obj.left,
      height: obj.top + obj.height + 8,
      inputTop: obj.top + (obj.height - 30) / 2,
      arrowTop: obj.top + (obj.height - 32) / 2,
    }, () => {
      wx.getSystemInfo({
        success: (res) => {
          this.setData({
            //略小，避免误差带来的影响
            dropScreenH: this.data.height * 750 / res.windowWidth + 86,
            drawerH: res.windowHeight - res.windowWidth / 750 * 100 - this.data.height
          })
        }
      })
    });

    tui.request(this.api.goodsList,"GET",{
      pageSize: 20,
      pageNum: 1,
      goodsName: options.key
    },false,false,false).then(res=>{
      this.setData({
        productList: res.rows
      })
    })
  },
  btnDropChange: function (e) {
    let index = e.currentTarget.dataset.index;
    let arr = JSON.parse(JSON.stringify(this.data.attrArr[index].list));
    if (arr.length === 0) {
      this.btnCloseDrop();
      let isActive = `attrArr[${index}].isActive`;
      this.setData({
        [isActive]: !this.data.attrArr[index].isActive
      })
    } else {
      let isActive = `attrArr[${index}].isActive`;
      this.setData({
        attrData: arr,
        attrIndex: index,
        dropScreenShow: true,
        [isActive]: false
      }, () => {
        this.setData({
          scrollTop: 0
        })
      })
    }
  },
  btnSelected: function (e) {
    let index = e.currentTarget.dataset.index;
    let selected = `attrData[${index}].selected`;
    this.setData({
      [selected]: !this.data.attrData[index].selected
    })
  },
  reset() {
    let arr = this.data.attrData;
    for (let item of arr) {
      item.selected = false;
    }
    this.setData({
      attrData: arr
    })
  },
  btnCloseDrop() {
    this.setData({
      scrollTop: 0,
      dropScreenShow: false,
      attrIndex: -1
    })
  },
  btnSure: function () {
    let index = this.data.attrIndex;
    let arr = this.data.attrData;
    let active = false;
    let attrName = "";
    //这里只是为了展示选中效果,并非实际场景
    for (let item of arr) {
      if (item.selected) {
        active = true;
        attrName += attrName ? ";" + item.name : item.name
      }
    }
    let isActive = `attrArr[${index}].isActive`;
    let selectedName = `attrArr[${index}].selectedName`;
    this.btnCloseDrop();
    this.setData({
      [isActive]: active,
      [selectedName]: attrName
    })
  },
  showDropdownList: function () {
    this.setData({
      selectH: 246,
      tabIndex: 0
    })
  },
  hideDropdownList: function () {
    this.setData({
      selectH: 0
    })
  },
  dropdownItem: function (e) {
    let index = e.currentTarget.dataset.index;
    let arr = this.data.dropdownList;
    for (let i = 0; i < arr.length; i++) {
      if (i === index) {
        arr[i].selected = true;
      } else {
        arr[i].selected = false;
      }
    }
    this.setData({
      dropdownList: arr,
      selectedName: index == 0 ? '综合' : '价格',
      selectH: 0
    })
  },
  screen: function (e) {
    let index = e.currentTarget.dataset.index;
    this.hideDropdownList();
    this.btnCloseDrop();
    if (index == 0) {
      this.showDropdownList();
    } else if (index == 1) {
      this.setData({
        tabIndex:1
      })
    } else if (index == 2) {
      this.setData({
        isList:!this.data.isList
      })
    } else if (index == 3) {
      this.setData({
        drawer: true
      })
    }
  },
  closeDrawer: function () {
    this.setData({
      drawer: false
    })
  },
  back: function () {
    if (this.data.drawer) {
      this.closeDrawer();
    } else {
      wx.navigateBack();
    }
  },
  search: function () {
    wx.navigateTo({
      url: '/pages/common/search/search'
    });
  },
  toGoodsDetails(e){
    wx.navigateTo({
      url: '/pages/goods/details/index?id='+e.currentTarget.dataset.type,
    })
  },
  // onReachBottom: function () {
  //   if (!this.data.pullUpOn) return;
  //   this.setData({
  //     loadding: true
  //   }, () => {
  //     if (this.data.pageIndex == 4) {
  //       this.setData({
  //         loadding: false,
  //         pullUpOn: false
  //       })
  //     } else {
  //       let loadData = JSON.parse(JSON.stringify(this.data.productList));
  //       loadData = loadData.splice(0, 10)
  //       if (this.data.pageIndex == 1) {
  //         loadData = loadData.reverse();
  //       }
  //       this.setData({
  //         productList: this.data.productList.concat(loadData),
  //         pageIndex: this.data.pageIndex + 1,
  //         loadding: false
  //       })
  //     }
  //   })
  // }
})
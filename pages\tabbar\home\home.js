import tui from '../../../common/httpRequest'
Page({
  data: {
    api:{
      homeIcon: "/pet/homeIcon/app/list",
      homeBanner: "/pet/homeBanner/app/list",
      pagegoods: "/pet/goods/list/page",
      topGoods: "/pet/goods/app/top"
    },
    ossUrl:tui.ossUrl(),
    hotSearch: [],
    banner: [],
    category: [],
    topGoodsList: [],
    productList: [],
    pageIndex: 1,
    loadding: false,
    pullUpOn: true,
    total: 0,
    maxpageNum: 1,
    params: {
      pageSize: 10,
      pageNum: 1,
      status: '0'
    }
  },
  onLoad: function(){
    this.getBanner()
    this.getCategory()
    this.getTopGoods()
    this.getGoodsPage()
  },
  /**
   * 获取头部轮播
   */
  getBanner:function(){
    tui.request(this.data.api.homeBanner,"GET",null,false,false,false)
    .then(res=>{
      this.setData({
        banner: res.data,
      })
    })
  },
  toSearchPage: function() {
    // tui.toast("我进来啦")
    wx.navigateTo({
      url: '/pages/goods/search/search',
    })
  },
    /**
   * 获取分类
   */
  getCategory:function(){
    tui.request(this.data.api.homeIcon,"GET",null,false,false,false)
    .then(res=>{
      this.setData({
        category: res.data,
      })
    })
  },
  /**
   * 获取排行榜
   */
  getTopGoods:function(){
    tui.request(this.data.api.topGoods,"GET",this.data.params,false,false,false)
    .then(res=>{
      this.setData({
        topGoodsList: res.data
      })
    })
  },
  /**
   * 获取商品分页
   */
  getGoodsPage:function(){
    let that  = this
    tui.request(this.data.api.pagegoods,"GET",this.data.params,false,false,false)
    .then(res1=>{
      that.setData({
        productList: res1.rows,
        total: res1.total
      })
      that.setData({
        maxpageNum: that.calculateTotalPages(res1.total,that.data.params.pageSize)
      })
      // console.log("最大页数：",that.data.maxpageNum)
    })
  },
  /**
   * 跳转详情
   */
  toGoodsDetails(e){
    wx.navigateTo({
      url: '/pages/goods/details/index?id='+e.currentTarget.dataset.type,
    })
  },
  toGoodsListPage:function(){
    tui.href('/pages/goods/list/index')
  },
  search: function() {
    tui.href('/pages/common/search/search')
  },
  /**
   * 计算分页
   * @param {*} total 
   * @param {*} pageSize 
   */
  calculateTotalPages:function (total, pageSize) {
    if (total <= 0 || pageSize <= 0) return 0; // 无效参数直接返回 0
    return Math.ceil(total / pageSize);
  },
  onPullDownRefresh: function() {
    let loadData = JSON.parse(JSON.stringify(this.data.productList));
    loadData = loadData.splice(0, 10)
    this.setData({
      productList: loadData,
      pageIndex: 1,
      pullUpOn: true,
      loadding: false
    })
    wx.stopPullDownRefresh()
	},
	onReachBottom: function() {
	  // if (!this.data.pullUpOn) return;
    // this.setData({
    //   loadding: true
    // }, () => {
    //   if (this.data.pageIndex == 4) {
    //     this.setData({
    //       loadding: false,
    //       pullUpOn: false
    //     })
    //   } else {
    //     let loadData = JSON.parse(JSON.stringify(this.data.productList));
    //     loadData = loadData.splice(0, 10)
    //     if (this.data.pageIndex == 1) {
    //       loadData = loadData.reverse();
    //     }
    //     this.setData({
    //       productList: this.data.productList.concat(loadData),
    //       pageIndex: this.data.pageIndex + 1,
    //       loadding: false
    //     })
    //   }
    // })
	},
  onShareAppMessage: function () {
      return {
        title:"点晶宠物用品批发平台"
      }
  }
})
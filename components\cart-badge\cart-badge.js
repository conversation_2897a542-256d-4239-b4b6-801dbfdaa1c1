const cartManager = require('../../utils/cartManager');

Component({
  properties: {
    // 显示位置：right-top, left-top, right-bottom, left-bottom
    position: {
      type: String,
      value: 'right-top'
    },
    // 徽章大小
    size: {
      type: Number,
      value: 20
    },
    // 徽章颜色
    color: {
      type: String,
      value: '#ff4757'
    },
    // 文字颜色
    textColor: {
      type: String,
      value: '#fff'
    },
    // 最大显示数量，超过显示99+
    maxCount: {
      type: Number,
      value: 99
    },
    // 是否显示0
    showZero: {
      type: Boolean,
      value: false
    }
  },

  data: {
    cartCount: 0
  },

  lifetimes: {
    attached() {
      this.updateCartCount();
      this.registerCartListener();
    },
    
    detached() {
      this.unregisterCartListener();
    }
  },

  methods: {
    /**
     * 更新购物车数量
     */
    updateCartCount() {
      const count = cartManager.getCartItemCount();
      this.setData({
        cartCount: count
      });
    },

    /**
     * 注册购物车数据变化监听器
     */
    registerCartListener() {
      const componentId = `cart-badge-${this.data.componentId || Date.now()}`;
      cartManager.addPageListener(componentId, (data) => {
        this.setData({
          cartCount: data.cartItemCount
        });
      });
      this.componentId = componentId;
    },

    /**
     * 注销购物车数据变化监听器
     */
    unregisterCartListener() {
      if (this.componentId) {
        cartManager.removePageListener(this.componentId);
      }
    },

    /**
     * 格式化显示数量
     */
    formatCount(count) {
      if (count <= 0 && !this.properties.showZero) {
        return '';
      }
      if (count > this.properties.maxCount) {
        return `${this.properties.maxCount}+`;
      }
      return count.toString();
    },

    /**
     * 点击徽章事件
     */
    onBadgeClick() {
      this.triggerEvent('click', {
        cartCount: this.data.cartCount
      });
    }
  }
});

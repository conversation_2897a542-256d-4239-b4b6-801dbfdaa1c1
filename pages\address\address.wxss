.tui-address {
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 160rpx;
}
.tui-address-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tui-address-main {
  width: 600rpx;
  height: 70rpx;
  display: flex;
  font-size: 30rpx;
  line-height: 86rpx;
  padding-left: 30rpx;
}

.tui-address-name {
  width: 120rpx;
  height: 60rpx;
}

.tui-address-tel {
  margin-left: 12rpx;
}

.tui-address-detail {
  font-size: 24rpx;
  word-break: break-all;
  padding-bottom: 25rpx;
  padding-left: 25rpx;
  padding-right: 120rpx;
}

.tui-address-label {
  padding: 5rpx 8rpx;
  flex-shrink: 0;
  background: #ac9157;
  border-radius: 6rpx;
  color: #fff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 25rpx;
  line-height: 25rpx;
  transform: scale(0.8);
  transform-origin: center center;
  margin-right: 6rpx;
}

.tui-address-imgbox {
  width: 80rpx;
  height: 100rpx;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  right: 10rpx;
}

.tui-address-img {
  width: 36rpx;
  height: 36rpx;
}

.tui-address-new {
  width: 100%;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 999;
  padding: 20rpx 60rpx 30rpx;
  box-sizing: border-box;
  background: #f8f8f8;
}

.tui-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}
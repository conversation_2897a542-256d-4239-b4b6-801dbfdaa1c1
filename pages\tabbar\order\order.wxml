<view class="container">
		<tui-tabs tabs="{{tabs}}" isFixed="{{scrollTop>=0}}" currentTab="{{currentTab}}" selectedColor="#E41F19" sliderBgColor="#E41F19"
		  bindchange="change" itemWidth="20%"></tui-tabs>
		<!--选项卡逻辑自己实现即可，此处未做处理-->
		<view class="{{scrollTop>=0?'tui-order-list':''}}">
			<view class="tui-order-item" wx:for="{{0}}" wx:for-item="model" wx:for-index="orderIndex" wx:key="orderIndex">
				<tui-list-cell hover="{{false}}" lineLeft="0">
					<view class="tui-goods-title">
						<view>订单号：T201910000</view>
						<view class="tui-order-status">已完成</view>
					</view>
				</tui-list-cell>
				<block wx:for="{{2}}" wx:key="index">
					<tui-list-cell padding="0"  bindclick="detail">
						<view class="tui-goods-item">
							<image src="/static/images/mall/product/{{index+3}}.jpg" class="tui-goods-img"></image>
							<view class="tui-goods-center">
								<view class="tui-goods-name">欧莱雅（LOREAL）奇焕光彩粉嫩透亮修颜霜 30ml（欧莱雅彩妆 BB霜 粉BB 遮瑕疵 隔离）</view>
								<view class="tui-goods-attr">黑色，50ml</view>
							</view>
							<view class="tui-price-right">
								<view>￥298.00</view>
								<view>x2</view>
							</view>
						</view>
					</tui-list-cell>
				</block>
				<tui-list-cell hover="{{false}}" unlined>
					<view class="tui-goods-price">
						<view>共4件商品 合计：</view>
						<view class="tui-size-24">￥</view>
						<view class="tui-price-large">1192</view>
						<view class="tui-size-24">.00</view>
					</view>
				</tui-list-cell>
				<view class="tui-order-btn">
					<view class="tui-btn-ml">
						<tui-button type="black" plain width="152rpx" height="56rpx" size="26" shape="circle" bindclick="invoiceDetail">查看发票</tui-button>
					</view>
					<view class="tui-btn-ml">
						<tui-button type="danger" plain width="152rpx" height="56rpx" size="26" shape="circle">再次购买</tui-button>
					</view>
          <view class="tui-btn-ml">
						<tui-button type="black" plain width="152rpx" height="56rpx" size="26" shape="circle">删除订单</tui-button>
					</view>
				</view>
			</view>
      
		</view>
		<!--加载loadding-->
		<tui-loadmore wx:if="{{loadding}}" index="3" type="red"></tui-loadmore>
		<tui-nomore wx:if="{{!pullUpOn}}" backgroundColor="#fafafa"></tui-nomore>
		<!--加载loadding-->

	</view>
.tui-addr-box {
  padding: 20rpx 0;
}

.tui-line-cell {
  width: 100%;
  padding: 24rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.tui-title {
  width: 180rpx;
  font-size: 28rpx;
}

.tui-title-city-text {
  width: 180rpx;
  height: 40rpx;
  display: block;
  line-height: 46rpx;
}

.tui-input {
  width: 500rpx;
}

.tui-input-city {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  padding-right: 30rpx;
}

.tui-phcolor {
  color: #ccc;
  font-size: 28rpx;
}
.tui-cell-title{
  font-size: 28rpx;
}
.tui-addr-label {
  margin-left: 70rpx;
}

.tui-label-item {
  width: 76rpx;
  height: 40rpx;
  border: 1rpx solid rgb(136, 136, 136);
  border-radius: 6rpx;
  font-size: 26rpx;
  text-align: center;
  line-height: 40rpx;
  margin-right: 20rpx;
  display: inline-block;
  transform: scale(0.9);
}
.tui-label-active{
  background: #ac9157;
  border-color:#ac9157;
  color: #fff;
}
.tui-swipe-cell {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 10rpx 24rpx;
  box-sizing: border-box;
  border-radius: 6rpx;
  overflow: hidden;
  font-size: 28rpx;
}

.tui-switch-small {
  transform: scale(0.8);
  transform-origin: 100% center;
}


.tui-switch-small .wx-switch-input {
  margin: 0 !important;
}


.tui-addr-save {
  padding: 24rpx 60rpx;
  margin-top: 100rpx;
}

.tui-del {
  padding: 24rpx 60rpx;
}
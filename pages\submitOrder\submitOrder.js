import tui from '../../common/httpRequest'
const cartManager = require('../../utils/cartManager');

Page({
  data: {
    webURL: '/static/images/cafe/',
    ossUrl: tui.ossUrl(),
    way: 2,
    selectedItems: [], // 选中的商品列表
    totalPrice: 0, // 商品总价
    shippingFee: 0, // 运费
    discountAmount: 0.20, // 积分抵扣金额
    finalPrice: 0, // 最终支付金额
    selectedAddress: null, // 选中的收货地址
    remark: '', // 备注信息
    totalQuantity: 0 // 商品总数量
  },

  onLoad(options) {
    this.loadSelectedItems();
    this.calculatePrices();
  },

  onShow() {
    // 页面显示时重新加载数据，防止从地址页面返回时数据不同步
    this.loadSelectedItems();
    this.calculatePrices();
  },

  /**
   * 加载购物车中选中的商品
   */
  loadSelectedItems() {
    const selectedItems = cartManager.getSelectedItems();

    if (selectedItems.length === 0) {
      // 如果没有选中商品，提示用户并返回购物车
      wx.showModal({
        title: '提示',
        content: '请先在购物车中选择要结算的商品',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/tabbar/cart/index'
          });
        }
      });
      return;
    }

    const totalQuantity = cartManager.getSelectedCount();
    const totalPrice = cartManager.getSelectedTotalPrice();

    this.setData({
      selectedItems,
      totalQuantity,
      totalPrice
    });
  },

  /**
   * 计算各种价格
   */
  calculatePrices() {
    const { totalPrice, shippingFee, discountAmount } = this.data;
    const finalPrice = Math.max(0, totalPrice + shippingFee - discountAmount);

    this.setData({
      finalPrice
    });
  },

  /**
   * 切换配送方式
   */
  switchShipping(e) {
    const way = parseInt(e.detail.value);
    let shippingFee = 0;

    // 根据配送方式计算运费
    if (way === 2) { // 快递
      shippingFee = this.data.totalPrice >= 99 ? 0 : 10; // 满99免运费
    } else if (way === 3) { // 自提
      shippingFee = 0;
    }

    this.setData({
      way,
      shippingFee
    }, () => {
      this.calculatePrices();
    });
  },

  /**
   * 选择收货地址
   */
  selectAddr() {
    wx.navigateTo({
      url: '../address/address?from=order'
    });
  },

  /**
   * 备注输入
   */
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  /**
   * 提交订单
   */
  submitOrder() {
    // 验证必填信息
    if (this.data.way === 2 && !this.data.selectedAddress) {
      tui.toast('请选择收货地址');
      return;
    }

    if (this.data.selectedItems.length === 0) {
      tui.toast('没有可结算的商品');
      return;
    }

    // 构建订单数据
    const orderData = {
      items: this.data.selectedItems,
      totalPrice: this.data.totalPrice,
      shippingFee: this.data.shippingFee,
      discountAmount: this.data.discountAmount,
      finalPrice: this.data.finalPrice,
      shippingWay: this.data.way,
      address: this.data.selectedAddress,
      remark: this.data.remark,
      createTime: Date.now()
    };

    console.log('订单数据:', orderData);

    // 这里可以调用提交订单的API
    wx.showLoading({
      title: '提交中...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();

      // 提交成功后清除购物车中的选中商品
      const result = cartManager.removeSelectedItems();

      wx.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          setTimeout(() => {
            // 跳转到订单详情或订单列表页面
            wx.switchTab({
              url: '/pages/tabbar/order/order'
            });
          }, 2000);
        }
      });
    }, 1500);
  }
})
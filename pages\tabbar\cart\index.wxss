.container {
  padding-bottom: 120rpx;
}

.tui-mtop {
  margin-top: 24rpx;
}

.tui-edit-goods {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  padding: 24rpx 30rpx 0 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #333;
  font-size: 24rpx;
}

.tui-edit-buttons {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.tui-goods-num {
  font-weight: bold;
  color: #e41f19;
}

/* 购物车空状态样式 */
.tui-empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.tui-empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.tui-empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.tui-empty-desc {
  font-size: 28rpx;
  color: #999;
}

.tui-cart-cell {
  width: 100%;
  border-radius: 12rpx;
  background: #FFFFFF;
  padding: 40rpx 0;
  overflow: hidden;
}

.tui-goods-item {
  display: flex;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.tui-checkbox {
  min-width: 70rpx;
  display: flex;
  align-items: center;
}

.tui-checkbox .wx-checkbox-input {
  width: 40rpx;
  height: 40rpx;
  margin-right: 0 !important;
  border-radius: 50% !important;
  transform: scale(0.8);
  border-color: #d1d1d1 !important;
}

.tui-checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background: #eb0909;
  width: 44rpx !important;
  height: 44rpx !important;
  border: none;
}

.tui-goods-img {
  width: 200rpx;
  height: 150rpx !important;
  border-radius: 12rpx;
  flex-shrink: 0;
  display: block;
}

.tui-goods-info {
  width: 100%;
  padding-left: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  box-sizing: border-box;
  overflow: hidden;
}

.tui-goods-title {
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  font-size: 24rpx;
  color: #333;
}

.tui-goods-model {
  max-width: 100%;
  color: #333;
  background: #F5F5F5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  box-sizing: border-box;
}

.tui-model-text {
  max-width: 100%;
  transform: scale(0.9);
  transform-origin: 0 center;
  font-size: 24rpx;
  line-height: 32rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tui-price-box {
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.tui-goods-price {
  font-size: 34rpx;
  font-weight: 500;
  color: #e41f19;
}


.tui-activity {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx 20rpx 100rpx;
  box-sizing: border-box;
}

.tui-buy {
  display: flex;
  align-items: center
}

.tui-bold {
  font-weight: bold;
}

.tui-sub-info {
  max-width: 532rpx;
  font-size: 24rpx;
  line-height: 24rpx;
  padding: 20rpx 30rpx 10rpx 30rpx;
  box-sizing: border-box;
  color: #333;
  transform: scale(0.8);
  transform-origin: 100% center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: auto
}

.tui-invalid-text {
  width: 66rpx;
  margin-right: 4rpx;
  text-align: center;
  font-size: 24rpx;
  color: #fff;
  background: rgba(0, 0, 0, .3);
  transform: scale(0.8);
  transform-origin: center center;
  border-radius: 4rpx;
  flex-shrink: 0;
}

.tui-gray {
  color: #B2B2B2 !important;
}

.tui-goods-invalid {
  color: #555;
  font-size: 24rpx;
}

.tui-flex-center {
  align-items: center !important;
}

.tui-invalid-ptop {
  padding-top: 40rpx;
}

.tui-tabbar {
  width: 100%;
  height: 100rpx;
  background: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  z-index: 99999;
}

.tui-tabbar::before {
  content: '';
  width: 100%;
  border-top: 1rpx solid #d9d9d9;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.tui-checkAll {
  display: flex;
  align-items: center;
}

.tui-checkbox-pl {
  padding-left: 12rpx;
}

.tui-total-price {
  padding-left: 30rpx;
  font-size: 30rpx !important;
}

/*猜你喜欢*/
.tui-youlike {
  padding-left: 12rpx
}

.tui-product-list {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.tui-product-container {
  flex: 1;
  margin-right: 2%;
}

.tui-product-container:last-child {
  margin-right: 0;
}

.tui-pro-item {
  width: 100%;
  margin-bottom: 4%;
  background: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  overflow: hidden;
}

.tui-pro-img {
  width: 100%;
  display: block;
}

.tui-pro-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 20rpx;
}

.tui-pro-tit {
  color: #2e2e2e;
  font-size: 26rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.tui-pro-price {
  padding-top: 18rpx;
}

.tui-sale-price {
  font-size: 34rpx;
  font-weight: 500;
  color: #e41f19;
}

.tui-factory-price {
  font-size: 24rpx;
  color: #a0a0a0;
  text-decoration: line-through;
  padding-left: 12rpx;
}

.tui-pro-pay {
  padding-top: 10rpx;
  font-size: 24rpx;
  color: #656565;
}
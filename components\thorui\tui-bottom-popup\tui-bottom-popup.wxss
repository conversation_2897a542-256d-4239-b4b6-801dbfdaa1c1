.tui-bottom-popup {
  width: 100%;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transform: translate3d(0, 100%, 0);
  transform-origin: center;
  transition: all 0.3s ease-in-out;
  min-height: 20rpx;
}
.tui-popup-radius {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding-bottom: env(safe-area-inset-bottom);
  overflow: hidden;
}

.tui-popup-show {
  /* transform: translate3d(0, 0, 0); */
  opacity: 1;
}

.tui-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out;
  opacity: 0;
  visibility: hidden;
}

.tui-mask-show {
  opacity: 1;
  visibility: visible;
}
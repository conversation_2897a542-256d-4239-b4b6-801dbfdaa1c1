<view class="container">
		<view class="tui-edit-goods">
			<view>购物车共<text class="tui-goods-num">{{dataList.length}}</text>件商品</view>
			<view class="tui-edit-buttons">
				<tui-button type="gray" plain="{{true}}" shape="circle" width="120rpx" height="60rpx" size="24" bindclick="clearCart" wx:if="{{dataList.length > 0}}">清空</tui-button>
				<tui-button type="gray" plain="{{true}}" shape="circle" width="160rpx" height="60rpx" size="24" bindclick="editGoods">{{isEdit?"完成":"编辑商品"}}</tui-button>
			</view>
		</view>
		<!-- 购物车为空时显示 -->
		<view class="tui-empty-cart" wx:if="{{dataList.length === 0}}">
			<view class="tui-empty-icon">🛒</view>
			<view class="tui-empty-text">购物车是空的</view>
			<view class="tui-empty-desc">快去添加一些商品吧~</view>
		</view>

		<!-- 购物车商品列表 -->
		<checkbox-group bindchange="buyChange" wx:else>
			<view class="tui-cart-cell tui-mtop" wx:for="{{dataList}}" wx:for-item="goods" wx:key="id">
				<tui-swipe-action actions="{{actions}}" bindclick="handlerButton">
						<view class="tui-goods-item" slot="content">
							<label class="tui-checkbox">
								<checkbox value="{{goods.id}}" checked="{{goods.selected}}" color="#fff"></checkbox>
							</label>
							<image src="{{ossUrl + goods.url}}" class="tui-goods-img" />
							<view class="tui-goods-info">
								<view class="tui-goods-title">
									{{goods.goodsName}}
								</view>
								<view class="tui-price-box">
									<view class="tui-goods-price">￥{{goods.wholesalePrice}}</view>
										<tui-numberbox value="{{goods.num}}" height="36" width="64" min="1" index="{{index}}" bindchange="changeNum"></tui-numberbox>
								</view>
							</view>
						</view>
				</tui-swipe-action>
			</view>
		</checkbox-group>

		<!--tabbar-->
		<view class="tui-tabbar" wx:if="{{dataList.length > 0}}">
			<view class="tui-checkAll">
				<checkbox-group bindchange="checkAll">
					<label class="tui-checkbox">
						<checkbox checked="{{isAll}}" color="#fff"></checkbox>
						<text class="tui-checkbox-pl">全选</text>
					</label>
				</checkbox-group>
				<view class="tui-total-price" wx:if="{{!isEdit}}">合计:<text class="tui-bold">￥{{parse.getPrice(totalPrice)}}</text> </view>
			</view>
			<view>
				<tui-button width="200rpx" height="70rpx" size="30" type="danger" shape="circle" wx:if="{{!isEdit}}" bindclick="btnPay">去结算({{buyNum}})</tui-button>
				<tui-button width="200rpx" height="70rpx" size="30" type="danger" shape="circle" plain wx:else bindclick="deleteSelected">删除选中</tui-button>
			</view>
		</view>
		<!--加载loadding-->
		<tui-loadmore wx:if="{{loadding}}" index="3" type="red"></tui-loadmore>
	</view>

  <wxs module="parse">
  module.exports={
    getPrice:function(price) {
				price = price || 0;
				return price.toFixed(2)
			}
  }
  
  </wxs>
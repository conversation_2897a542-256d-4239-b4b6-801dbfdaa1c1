<view class="tui-datetime-picker">
	<view class="tui-mask {{isShow?'tui-mask-show':''}}" catchtouchmove="stop" bindtap="hide"></view>
	<view class="tui-header {{isShow?'tui-show':''}}">
		<view class="tui-picker-header {{radius?'tui-date-radius':''}}" catchtouchmove="stop">
			<view class="tui-btn-picker" style="color: {{cancelColor}}" hover-class="tui-opacity" hover-stay-time="150" bindtap="hide">取消</view>
			<view class="tui-btn-picker" style="color: {{color}}" hover-class="tui-opacity" hover-stay-time="150" bindtap="btnFix">确定</view>
		</view>
		<view class="tui-date-header" wx:if="{{unitTop}}">
			<view class="tui-date-unit" wx:if="{{type < 4 || type == 7}}">年</view>
			<view class="tui-date-unit" wx:if="{{type < 4 || type == 7}}">月</view>
			<view class="tui-date-unit" wx:if="{{type == 1 || type == 2 || type == 7}}">日</view>
			<view class="tui-date-unit" wx:if="{{type == 1 || type == 4 || type == 5 || type == 7}}">时</view>
			<view class="tui-date-unit" wx:if="{{type == 1 || type > 3}}">分</view>
			<view class="tui-date-unit" wx:if="{{type > 4}}">秒</view>
		</view>
		<view class="tui-picker-body">
			<picker-view value="{{value}}" bindchange="change" class="tui-picker-view">
				<picker-view-column wx:if="{{!reset && (type < 4 || type == 7)}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}" wx:for="{{years}}" wx:key="index">
						{{ item }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">年</text>
					</view>
				</picker-view-column>
				<picker-view-column wx:if="{{!reset && (type < 4 || type == 7)}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}" wx:for="{{months}}" wx:key="index">
						{{ parse.formatNum(item) }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">月</text>
					</view>
				</picker-view-column>
				<picker-view-column wx:if="{{!reset && (type == 1 || type == 2 || type == 7)}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}" wx:for="{{days}}" wx:key="index">
						{{ parse.formatNum(item) }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">日</text>
					</view>
				</picker-view-column>
				<picker-view-column wx:if="{{!reset && (type == 1 || type == 4 || type == 5 || type == 7)}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}" wx:for="{{hours}}" wx:key="index">
						{{ parse.formatNum(item) }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">时</text>
					</view>
				</picker-view-column>
				<picker-view-column wx:if="{{!reset && (type == 1 || type > 3)}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}"  wx:for="{{minutes}}" wx:key="index">
						{{ parse.formatNum(item) }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">分</text>
					</view>
				</picker-view-column>
				<picker-view-column wx:if="{{!reset && type > 4}}">
					<view class="tui-column-item {{!unitTop && type == 7?'tui-font-size_32':''}}"  wx:for="{{seconds}}" wx:key="index">
						{{ parse.formatNum(item) }}
						<text class="tui-unit-text" wx:if="{{!unitTop}}">秒</text>
					</view>
				</picker-view-column>
			</picker-view>
		</view>
	</view>
</view>
<wxs module="parse">
	module.exports = {
		formatNum: function(num) {
			return num < 10 ? "0" + num : num + "";
		}
	}
</wxs>
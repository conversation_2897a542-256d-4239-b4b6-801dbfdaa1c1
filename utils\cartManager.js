/**
 * 购物车管理工具类
 * 提供购物车的本地数据处理、计算和缓存功能
 */

const CART_STORAGE_KEY = 'carts_info';

class CartManager {
  constructor() {
    this.cartItems = this.getCartFromStorage();
  }

  /**
   * 从本地存储获取购物车数据
   * @returns {Array} 购物车商品列表
   */
  getCartFromStorage() {
    try {
      const cartData = wx.getStorageSync(CART_STORAGE_KEY);
      return cartData && Array.isArray(cartData) ? cartData : [];
    } catch (error) {
      console.error('获取购物车数据失败:', error);
      return [];
    }
  }

  /**
   * 保存购物车数据到本地存储
   * @param {Array} cartItems 购物车商品列表
   */
  saveCartToStorage(cartItems = this.cartItems) {
    try {
      wx.setStorageSync(CART_STORAGE_KEY, cartItems);
      this.cartItems = cartItems;
    } catch (error) {
      console.error('保存购物车数据失败:', error);
    }
  }

  /**
   * 添加商品到购物车
   * @param {Object} product 商品信息
   * @param {number} quantity 数量，默认为1
   * @returns {Object} 操作结果
   */
  addToCart(product, quantity = 1) {
    if (!product || !product.id) {
      return { success: false, message: '商品信息不完整' };
    }

    const cartItems = [...this.cartItems];
    const existingItemIndex = cartItems.findIndex(item => item.id === product.id);

    if (existingItemIndex > -1) {
      // 商品已存在，累加数量
      cartItems[existingItemIndex].num += quantity;
    } else {
      // 新商品，添加到购物车
      const cartItem = {
        id: product.id,
        goodsName: product.goodsName,
        wholesalePrice: product.wholesalePrice,
        url: product.masterImg,
        num: quantity,
        selected: false, // 默认未选中
        addTime: Date.now() // 添加时间戳
      };
      cartItems.unshift(cartItem);
    }

    this.saveCartToStorage(cartItems);
    
    return {
      success: true,
      message: '添加成功',
      cartCount: this.getCartCount(),
      totalPrice: this.getSelectedTotalPrice()
    };
  }

  /**
   * 从购物车移除商品
   * @param {string|number} productId 商品ID
   * @returns {Object} 操作结果
   */
  removeFromCart(productId) {
    const cartItems = this.cartItems.filter(item => item.id !== productId);
    this.saveCartToStorage(cartItems);
    
    return {
      success: true,
      message: '删除成功',
      cartCount: this.getCartCount(),
      totalPrice: this.getSelectedTotalPrice()
    };
  }

  /**
   * 更新商品数量
   * @param {string|number} productId 商品ID
   * @param {number} quantity 新数量
   * @returns {Object} 操作结果
   */
  updateQuantity(productId, quantity) {
    if (quantity <= 0) {
      return this.removeFromCart(productId);
    }

    const cartItems = [...this.cartItems];
    const itemIndex = cartItems.findIndex(item => item.id === productId);
    
    if (itemIndex > -1) {
      cartItems[itemIndex].num = quantity;
      this.saveCartToStorage(cartItems);
      
      return {
        success: true,
        message: '更新成功',
        cartCount: this.getCartCount(),
        totalPrice: this.getSelectedTotalPrice()
      };
    }
    
    return { success: false, message: '商品不存在' };
  }

  /**
   * 切换商品选中状态
   * @param {string|number} productId 商品ID
   * @param {boolean} selected 是否选中
   * @returns {Object} 操作结果
   */
  toggleItemSelection(productId, selected = null) {
    const cartItems = [...this.cartItems];
    const itemIndex = cartItems.findIndex(item => item.id === productId);
    
    if (itemIndex > -1) {
      cartItems[itemIndex].selected = selected !== null ? selected : !cartItems[itemIndex].selected;
      this.saveCartToStorage(cartItems);
      
      return {
        success: true,
        selectedItems: this.getSelectedItems(),
        totalPrice: this.getSelectedTotalPrice(),
        selectedCount: this.getSelectedCount()
      };
    }
    
    return { success: false, message: '商品不存在' };
  }

  /**
   * 全选/取消全选
   * @param {boolean} selectAll 是否全选
   * @returns {Object} 操作结果
   */
  toggleSelectAll(selectAll = null) {
    const cartItems = [...this.cartItems];
    const shouldSelectAll = selectAll !== null ? selectAll : !this.isAllSelected();
    
    cartItems.forEach(item => {
      item.selected = shouldSelectAll;
    });
    
    this.saveCartToStorage(cartItems);
    
    return {
      success: true,
      isAllSelected: shouldSelectAll,
      selectedItems: this.getSelectedItems(),
      totalPrice: this.getSelectedTotalPrice(),
      selectedCount: this.getSelectedCount()
    };
  }

  /**
   * 清空购物车
   * @returns {Object} 操作结果
   */
  clearCart() {
    this.saveCartToStorage([]);
    return {
      success: true,
      message: '购物车已清空',
      cartCount: 0,
      totalPrice: 0
    };
  }

  /**
   * 删除选中的商品
   * @returns {Object} 操作结果
   */
  removeSelectedItems() {
    const cartItems = this.cartItems.filter(item => !item.selected);
    this.saveCartToStorage(cartItems);
    
    return {
      success: true,
      message: '删除成功',
      cartCount: this.getCartCount(),
      totalPrice: this.getSelectedTotalPrice()
    };
  }

  /**
   * 获取购物车商品总数量
   * @returns {number} 总数量
   */
  getCartCount() {
    return this.cartItems.reduce((total, item) => total + item.num, 0);
  }

  /**
   * 获取购物车商品种类数
   * @returns {number} 商品种类数
   */
  getCartItemCount() {
    return this.cartItems.length;
  }

  /**
   * 获取选中商品列表
   * @returns {Array} 选中的商品列表
   */
  getSelectedItems() {
    return this.cartItems.filter(item => item.selected);
  }

  /**
   * 获取选中商品总数量
   * @returns {number} 选中商品总数量
   */
  getSelectedCount() {
    return this.getSelectedItems().reduce((total, item) => total + item.num, 0);
  }

  /**
   * 计算选中商品总价
   * @returns {number} 总价
   */
  getSelectedTotalPrice() {
    return this.getSelectedItems().reduce((total, item) => {
      return total + (item.wholesalePrice * item.num);
    }, 0);
  }

  /**
   * 计算购物车总价（所有商品）
   * @returns {number} 总价
   */
  getTotalPrice() {
    return this.cartItems.reduce((total, item) => {
      return total + (item.wholesalePrice * item.num);
    }, 0);
  }

  /**
   * 检查是否全选
   * @returns {boolean} 是否全选
   */
  isAllSelected() {
    return this.cartItems.length > 0 && this.cartItems.every(item => item.selected);
  }

  /**
   * 获取购物车所有商品
   * @returns {Array} 购物车商品列表
   */
  getCartItems() {
    return [...this.cartItems];
  }

  /**
   * 检查商品是否在购物车中
   * @param {string|number} productId 商品ID
   * @returns {boolean} 是否在购物车中
   */
  isInCart(productId) {
    return this.cartItems.some(item => item.id === productId);
  }

  /**
   * 获取商品在购物车中的数量
   * @param {string|number} productId 商品ID
   * @returns {number} 商品数量
   */
  getItemQuantity(productId) {
    const item = this.cartItems.find(item => item.id === productId);
    return item ? item.num : 0;
  }

  /**
   * 格式化价格显示
   * @param {number} price 价格
   * @returns {string} 格式化后的价格
   */
  formatPrice(price) {
    return (price || 0).toFixed(2);
  }
}

// 创建单例实例
const cartManager = new CartManager();

module.exports = cartManager;

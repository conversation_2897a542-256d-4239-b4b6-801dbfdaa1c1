import tui from '../../common/httpRequest'
Page({
  data: {
    webURL: '/static/images/cafe/',
    phoneNumber: '',
    password: ''
  },
  loginPwd() {
    if (this.data.phoneNumber == '' || this.data.password == '') {
      tui.toast("用户名密码不能为空")
      return
    }
    let that = this
    wx.login({
      success(res) {
        if (res.code) {
          tui.request(
            "/app/auth/login",
            "POST",
            {
              "code": res.code,
              "username": that.data.phoneNumber,
              "password": that.data.password,
              "clientId": "e5cd7e4891bf95d1d19206ce24a7b32e",
              "tenantId": tui.tenantId,
              "grantType": "PurchaseAppPwd"
            },
            false,
            false,
            false
          ).then(res1 => {
            console.log(res1)
            if (res1.code == 200) {
              wx.setStorageSync('auth_token', res1.data.access_token)
              wx.setStorageSync('nickname', res1.data.nickname)
              wx.switchTab({
                url: '/pages/tabbar/home/<USER>'
              })
            } else {
              tui.toast(res1.msg)
            }
          })
        }
      }
    })

  },
  verifyLogin() {
    tui.href('../verifyLogin/verifyLogin')
  },
  getPhoneNumber(e) {
    wx.login({
      success(res) {
        if (res.code) {
          tui.request(
            "/app/auth/login",
            "POST",
            {
              "code": res.code,
              "phoneCode": e.detail.code,
              "clientId": "e5cd7e4891bf95d1d19206ce24a7b32e",
              "tenantId": tui.tenantId,
              "grantType": "PurchaseApp"
            },
            false,
            false,
            false
          ).then(res1 => {
            console.log(res1)
            if (res1.code == 200) {
              wx.setStorageSync('auth_token', res1.data.access_token)
              wx.setStorageSync('nickname', res1.data.nickname)
              wx.switchTab({
                url: '/pages/tabbar/home/<USER>'
              })
            } else {
              tui.toast(res1.msg)
            }
          })
        } else {
          console.log('登录失败！' + res.errMsg)
        }
      }
    })
  }
})
<view class="container">
		<!--header-->
		<view class="tui-header-box">
			<view class="tui-header"  style="width:{{width}}px;height:{{height}}px">
				<view class="tui-back" style="margin-top:{{arrowTop}}px"  bindtap="back"><tui-icon name="arrowleft" color="#000"></tui-icon></view>
				<view class="tui-searchbox tui-search-mr" style="margin-top:{{inputTop}}px"  bindtap="search">
					<icon type="search" size="13" color="#999"></icon>
					<text class="tui-search-text" wx:if="{{!searchKey}}">搜索商品</text>
					<view class="tui-search-key" wx:if="{{searchKey}}">
						<view class="tui-key-text">{{ searchKey }}</view>
						<tui-icon name="shut" size="12" color="#fff"></tui-icon>
					</view>
				</view>
			</view>
		</view>
		<!--header-->
		<!--screen-->
		<view class="tui-header-screen" style="top:{{height}}px">
			<view class="tui-screen-top">
				<view class="tui-top-item tui-icon-ml {{tabIndex == 0 ? 'tui-active tui-bold' : ''}}" data-index="0"  bindtap="screen">
					<view>{{ selectedName }}</view>
					<tui-icon name="{{selectH > 0 ? 'arrowup' : 'arrowdown'}}" size="14" color="{{tabIndex == 0 ? '#e41f19' : '#444'}}"></tui-icon>
				</view>
				<view class="tui-top-item {{tabIndex == 1 ? 'tui-active tui-bold' : ''}}"  bindtap="screen" data-index="1">销量</view>
				<view class="tui-top-item"  bindtap="screen" data-index="2">
					<tui-icon name="{{isList ? 'manage' : 'listview'}}" size="{{isList ? 22 : 18}}" bold="{{isList ? false : true}}" color="#333"></tui-icon>
				</view>
				<!-- <view class="tui-top-item tui-icon-ml"   data-index="3">
					<text>筛选</text>
					<tui-icon name="screen" size="12" color="#333" bold="{{true}}"></tui-icon>
				</view> -->

				<!--下拉选择列表--综合-->
				<view class="tui-dropdownlist {{selectH > 0 ? 'tui-dropdownlist-show' : ''}}" style="height:{{selectH}}rpx">
					<view
						class="tui-dropdownlist-item tui-icon-middle {{item.selected ? 'tui-bold' : ''}}"
						wx:for="{{dropdownList}}"
						wx:key="index"
						catchtap="dropdownItem"
						data-index="{{index}}"
					>
						<text class="tui-ml tui-middle">{{ item.name }}</text>
						<tui-icon name="check" size="16" color="#e41f19" bold="{{true}}" wx:if="{{item.selected}}"></tui-icon>
					</view>
				</view>
				<view class="tui-dropdownlist-mask {{selectH > 0 ? 'tui-mask-show' : ''}}" catchtap="hideDropdownList"></view>
				<!--下拉选择列表--综合-->
			</view>
		</view>
		<!--screen-->

		<!--list-->
		<view class="tui-product-list" style="margin-top:{{dropScreenH + 18}}rpx">
			<view class="tui-product-container">
				<block wx:for="{{productList}}" wx:key="index" wx:if="{{(index + 1) % 2 != 0 || isList}}">
					<!--商品列表-->
					<view class="tui-pro-item {{isList ? 'tui-flex-list' : ''}}" hover-class="hover" hover-start-time="150"   data-type="{{item.id}}" bindtap="toGoodsDetails" >
						<image src="{{ossUrl + item.masterImg}}" class="tui-pro-img {{isList ? 'tui-proimg-list' : ''}}" mode="widthFix" />
						<view class="tui-pro-content">
							<view class="tui-pro-tit">{{ item.goodsName }}</view>
							<view>
								<view class="tui-pro-price">
									<text class="tui-sale-price">￥{{ item.wholesalePrice }}</text>
									<text class="tui-factory-price">￥{{ item.retailPrice }}</text>
								</view>
								<view class="tui-pro-pay">{{ item.salesVolume }}人付款</view>
							</view>
						</view>
					</view>
					<!--商品列表-->
				</block>
			</view>
			<view class="tui-product-container" wx:if="{{!isList}}">
				<block wx:for="{{productList}}" wx:key="index" wx:if="{{(index + 1) % 2 == 0}}">
					<!--商品列表-->
					<view class="tui-pro-item {{isList ? 'tui-flex-list' : ''}}" hover-class="hover" hover-start-time="150"  data-type="{{item.id}}" bindtap="toGoodsDetails" >
						<image src="{{ossUrl + item.masterImg}}" class="tui-pro-img {{isList ? 'tui-proimg-list' : ''}}" mode="widthFix" />
						<view class="tui-pro-content">
							<view class="tui-pro-tit">{{ item.goodsName }}</view>
							<view>
								<view class="tui-pro-price">
									<text class="tui-sale-price">￥{{ item.wholesalePrice }}</text>
									<text class="tui-factory-price">￥{{ item.retailPrice }}</text>
								</view>
								<view class="tui-pro-pay">{{ item.salesVolume }}人付款</view>
							</view>
						</view>
					</view>
					<!--商品列表-->
				</block>
			</view>
		</view>

		<!--list-->

		<!--顶部下拉筛选弹层 属性-->
		<tui-top-dropdown backgroundColor="#f7f7f7" show="{{dropScreenShow}}" paddingbtm="{{110}}" translatey="{{dropScreenH}}"  bindclose="btnCloseDrop">
			<scroll-view class="tui-scroll-box" scroll-y scroll-top="{{scrollTop}}">
				<view class="tui-seizeaseat-20"></view>
				<view
					class="tui-drop-item tui-icon-middle {{item.selected ? 'tui-bold' : ''}}"
					wx:for="{{attrData}}"
					wx:key="index"
					catchtap="btnSelected"
					data-index="{{index}}"
				>
					<tui-icon name="check" size="16" color="#e41f19" bold="{{true}}" wx:if="{{item.selected}}"></tui-icon>
					<text class="tui-ml tui-middle">{{ item.name }}</text>
				</view>
				<view class="tui-seizeaseat-30"></view>
			</scroll-view>
			<view class="tui-drop-btnbox">
				<view class="tui-drop-btn tui-btn-white" hover-class="tui-white-hover" hover-stay-time="150"  bindtap="reset">重置</view>
				<view class="tui-drop-btn tui-btn-red" hover-class="tui-red-hover" hover-stay-time="150"  bindtap="btnSure">确定</view>
			</view>
		</tui-top-dropdown>
		<!---顶部下拉筛选弹层 属性-->

		<!--左抽屉弹层 筛选 -->
		<tui-drawer mode="right" visible="{{drawer}}"  bindclose="closeDrawer">
			<view class="tui-drawer-box" style="padding-top:{{height}}px">
				<scroll-view class="tui-drawer-scroll" scroll-y style="height:{{drawerH}}px">
					<view class="tui-drawer-title">
						<text class="tui-title-bold">价格区间</text>
						<view class="tui-attr-right">
							<tui-icon name="position-fill" color="#e41f19" size="14" class="tui-location"></tui-icon>
							<text class="tui-addr-left">北京朝阳区三环到四环之间</text>
						</view>
					</view>
					<view class="tui-drawer-content">
						<input placeholder-class="tui-phcolor" class="tui-input" placeholder="最低价" maxlength="11" type="number" />
						<tui-icon name="reduce" color="#333" size="14"></tui-icon>
						<input placeholder-class="tui-phcolor" class="tui-input" placeholder="最高价" maxlength="11" type="number" />
					</view>

					<view class="tui-drawer-title">
						<text class="tui-title-bold">全部分类</text>
						<view class="tui-all-box tui-icon-ml">
							<view class="tui-attr-right">全部</view>
							<tui-icon name="arrowdown" size="14" color="#444"></tui-icon>
						</view>
					</view>
					<view class="tui-drawer-content tui-flex-attr">
						<view class="tui-attr-item tui-btmItem-active"><view class="tui-attr-ellipsis">男装</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">女装</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">运动服饰</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">户外鞋服</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">文化</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">服饰配件</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">流行男鞋</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">艺术</view></view>
					</view>

					<view class="tui-drawer-title">
						<text class="tui-title-bold">品牌</text>
						<view class="tui-all-box tui-icon-ml">
							<view class="tui-attr-right tui-active ">花花公子，七匹狼（SEPTWOLVES）</view>
							<tui-icon name="arrowdown" size="14" color="#444"></tui-icon>
						</view>
					</view>
					<view class="tui-drawer-content tui-flex-attr">
						<view class="tui-attr-item tui-btmItem-active"><view class="tui-attr-ellipsis">花花公子</view></view>
						<view class="tui-attr-item tui-btmItem-active"><view class="tui-attr-ellipsis">七匹狼（SEPTWOLVES）</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">吉普</view></view>
					</view>

					<view class="tui-drawer-title">
						<text class="tui-title-bold">尺码</text>
						<view class="tui-all-box tui-icon-ml">
							<view class="tui-attr-right">全部</view>
							<tui-icon name="arrowup" size="14" color="#444"></tui-icon>
						</view>
					</view>
					<view class="tui-drawer-content tui-flex-attr">
						<view class="tui-attr-item tui-btmItem-active"><view class="tui-attr-ellipsis">27</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">28</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">29</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">30</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">31</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">32</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">33</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">34</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">35</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">36</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">37</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">38</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">39</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">40</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">41</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">42</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">43</view></view>
						<view class="tui-attr-item"><view class="tui-attr-ellipsis">44</view></view>
					</view>
					<view class="tui-safearea-bottom"></view>
				</scroll-view>
				<view class="tui-attr-btnbox">
					<view class="tui-attr-safearea">
						<view class="tui-drawer-btn tui-drawerbtn-black" hover-class="tui-white-hover" :hover-stay-time="150">重置</view>
						<view class="tui-drawer-btn tui-drawerbtn-primary" hover-class="tui-red-hover" :hover-stay-time="150"  bindtap="closeDrawer">确定(80万+件商品)</view>
					</view>
				</view>
			</view>
		</tui-drawer>
		<!--左抽屉弹层 筛选-->

		<!--加载loadding-->
		<tui-loadmore wx:if="{{loadding}}" index="3" type="red"></tui-loadmore>
		<tui-nomore wx:if="{{!pullUpOn && isList}}" backgroundColor="#f7f7f7"></tui-nomore>
		<!--加载loadding-->
	</view>
.tui-list-cell {
	position: relative;
	width: 100%;
	box-sizing: border-box;
}
.tui-radius {
	border-radius: 6rpx;
	overflow: hidden;
}

.tui-cell-hover {
	background-color: #f1f1f1 !important;
}

.tui-list-cell::after {
	content: '';
	position: absolute;
	border-bottom: 1px solid #eaeef1;
	-webkit-transform: scaleY(0.5) translateZ(0);
	transform: scaleY(0.5) translateZ(0);
	transform-origin: 0 100%;
	bottom: 0;
	right: 0;
	left: 0;
}

.tui-line-left::after {
	left: 30rpx !important;
}

.tui-line-right::after {
	right: 30rpx !important;
}

.tui-cell-unlined::after {
	border-bottom: 0 !important;
}

.tui-cell-arrow::before {
	content: ' ';
	height: 10px;
	width: 10px;
	border-width: 2px 2px 0 0;
	border-color: #c0c0c0;
	border-style: solid;
	-webkit-transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
	transform: matrix(0.5, 0.5, -0.5, 0.5, 0, 0);
	position: absolute;
	top: 50%;
	margin-top: -6px;
	right: 30rpx;
}
.tui-arrow-right::before {
	right: 0 !important;
}
.tui-arrow-gray::before {
	border-color: #666666 !important;
}
.tui-arrow-white::before {
	border-color: #ffffff !important;
}
.tui-arrow-warning::before {
	border-color: #ff7900 !important;
}
.tui-arrow-success::before {
	border-color: #19be6b !important;
}
.tui-arrow-danger::before {
	border-color: #eb0909 !important;
}
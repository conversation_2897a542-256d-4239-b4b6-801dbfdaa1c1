.tui-banner {
  width: 100%;
  height: 400rpx;
}

.tui-menu__box {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.tui-left__box {
  width: 22%;
  min-height: 300px;
  background-color: #fff;
}

.tui-left__sticky {
  position: sticky;
  left: 0;
  z-index: 1;
}

.tui-left__fixed {
  position: fixed;
  left: 0;
  z-index: 1;
}

.tui-right__box {
  width: 78%;
  min-height: 100%;
  background-color: #fff;
  padding-bottom: 100rpx;
}

.tui-menu__item {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
  color: #666;
  background-color: #f8f8f8;
}

.tui-menu__active {
  background-color: #fff;
  color: #000;
  font-weight: 600;
}

.tui-bottom__radius {
  border-bottom-right-radius: 20rpx;
}

.tui-top__radius {
  border-top-right-radius: 20rpx;
}

.tui-menu__item text {
  font-size: 26rpx;
}

.tui-menu__icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.tui-title {
  width: 100%;
  color: #ac9157;
  font-size: 28rpx;
  line-height: 28rpx;
  font-weight: 500;
  padding: 30rpx 30rpx 20rpx;
  box-sizing: border-box;
}

.tui-rec {
  padding: 50rpx 30rpx;
}

.tui-rec__list {
  padding-bottom: 10rpx;
}

.tui-goods__list {
  width: 100%;
}

.tui-img__box {
  width: 100%;
  position: relative;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.tui-rec__img {
  width: 100%;
  height: 260rpx;
  border-radius: 8rpx;
  display: block;
  background-color: #f8f8f8;
}

.tui-goods__box {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  padding: 0 60rpx 0 30rpx;
  box-sizing: border-box;
  font-size: 24rpx;
}

.tui-rec__title {
  font-size: 32rpx;
  font-weight: bold;
}

.tui-rec__attr {
  font-size: 24rpx;
  padding-top: 12rpx;
}

.tui-rec__price {
  padding-bottom: 24rpx;
}
.tui-goods__item {
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.tui-goods__img {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  margin-right: 16rpx;
  background-color: #f8f8f8;
}

.tui-plus__img {
  width: 56rpx;
  height: 56rpx;
  flex-shrink: 0;
  margin-left: auto;
}

.tui-goods__info {
  width: 56%;
  height: 132rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-content: space-between;
}

.tui-goods__title {
  width: 100%;
  font-size: 32rpx;
  line-height: 32rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tui-attr {
  font-size: 24rpx;
  color: #999;
}

.tui-order__bar {
  width: 100%;
  height: 100rpx;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 998;
  background-color: #fff;
  box-shadow: 1px 1px 10px 1px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.tui-bags__box {
  position: relative;
}

.tui-bags__img {
  width: 48rpx;
  height: 48rpx;
  display: block;
}

.tui-total__price {
  font-weight: bold;
  font-family: sans-serif;
  margin-left: 80rpx;
}

.tui-submit {
  margin-left: auto;
}

.tui-pbm__100 {
  padding-bottom: 100rpx;
}

.tui-order__modal {
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}
.goods-banner{
  height: 500rpx;
}
.tui-img__detail {
  width: 100%;
}

.tui-modal__box {
  width: 100%;
  padding: 60rpx 40rpx;
  box-sizing: border-box;
}

.tui-title__goods {
  font-size: 32rpx;
  font-weight: bold;
  padding-bottom: 20rpx;
}

.tui-attr__box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 18rpx;
}

.tui-attr__title {
  font-size: 24rpx;
  color: #999;
  flex-shrink: 0;
}

.tui-attr__all {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.tui-attr {
  width: 48rpx;
  height: 48rpx;
  border: 2px solid #b2b2b2;
  border-radius: 50%;
  font-size: 24rpx;
  line-height: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b2b2b2;
  margin: 6rpx 0 6rpx 20rpx;
}

.tui-attr__large {
  width: auto;
  min-width: 112rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  border-radius: 48px;
}

.tui-attr__active {
  border-color: #ac9157;
  color: #ac9157;
}

.tui-modal__bottom {
  margin-top: 50rpx;
  border-top: 1rpx solid #eee;
  padding-top: 50rpx;
}

.tui-price__box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tui-price {
  font-size: 36rpx;
  line-height: 36rpx;
  font-size: bold;
}

.tui-number__box {
  display: flex;
  align-items: center;
}

.tui-disabled {
  opacity: 0.5;
}

.tui-num {
  font-size: 32rpx;
  font-weight: 400;
  min-width: 80rpx;
  text-align: center;
}

.tui-btn__box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30rpx;
}
.tui-attr__selected {
  font-size: 24rpx;
  transform: scale(0.9);
  transform-origin: 0 0;
}

.tui-name {
  padding-bottom: 4rpx;
}

.tui-cancel__icon {
  position: absolute;
  left: 0;
  top: 0;
  padding: 12rpx 0 0 20rpx;
}

.tui-popup__header {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.tui-empty__box {
  display: flex;
  align-items: center;
  color: #999;
}

.tui-empty__text {
  padding-left: 6rpx;
  font-size: 24rpx;
  line-height: 24rpx;
}

.tui-cart__list {
  width: 100%;
  min-height: 300rpx;
  max-height: 600rpx;
}

.tui-cart__item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tui-title__box {
  width: 60%;
}

.tui-title__text {
  font-size: 30rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 12rpx;
}

.tui-attr__text {
  font-size: 24rpx;
  color: #999;
}
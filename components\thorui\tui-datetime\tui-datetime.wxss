.tui-datetime-picker {
  position: relative;
  z-index: 999;
}

.tui-picker-view {
  height: 100%;
  box-sizing: border-box;
}

.tui-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.tui-mask-show {
  visibility: visible !important;
  opacity: 1 !important;
}

.tui-header {
  z-index: 9999;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  transition: all 0.3s ease-in-out;
  transform: translateY(100%);
}

.tui-date-header {
  width: 100%;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  font-size: 26rpx;
  line-height: 26rpx;
  color: #555;
  box-shadow: 0 15rpx 10rpx -15rpx #efefef;
  position: relative;
  z-index: 2;
}

.tui-date-unit {
  flex: 1;
  text-align: center;
}

.tui-show {
  transform: translateY(0);
}

.tui-picker-header {
  width: 100%;
  height: 90rpx;
  padding: 0 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  font-size: 32rpx;
  background-color: #fff;
  position: relative;
}

.tui-date-radius {
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.tui-picker-header::after {
  content: '';
  position: absolute;
  border-bottom: 1rpx solid #eaeef1;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  bottom: 0;
  right: 0;
  left: 0;
}

.tui-picker-body {
  width: 100%;
  height: 520rpx;
  overflow: hidden;
  background-color: #fff;
}

.tui-column-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
}

.tui-font-size_32 {
  font-size: 32rpx !important;
}

.tui-unit-text {
  font-size: 24rpx !important;
  padding-left: 8rpx;
}

.tui-btn-picker {
  padding: 16rpx;
  box-sizing: border-box;
  text-align: center;
  text-decoration: none;
}

.tui-opacity {
  opacity: 0.5;
}
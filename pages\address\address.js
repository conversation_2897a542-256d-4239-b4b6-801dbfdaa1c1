import tui from '../../common/httpRequest'
Page({
  data: {
    addressList: [
      {
        id: 1,
        name: '张三',
        phone: '13800138000',
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        detail: '科技园南区深南大道10000号',
        isDefault: true,
        fullAddress: '广东省深圳市南山区科技园南区深南大道10000号'
      },
      {
        id: 2,
        name: '李四',
        phone: '13900139000',
        province: '广东省',
        city: '广州市',
        district: '天河区',
        detail: '珠江新城花城大道100号',
        isDefault: false,
        fullAddress: '广东省广州市天河区珠江新城花城大道100号'
      }
    ],
    fromOrder: false // 是否从订单页面跳转过来
  },

  onLoad(options) {
    // 检查是否从订单页面跳转过来
    if (options.from === 'order') {
      this.setData({
        fromOrder: true
      });
    }
  },

  // 选择地址（从订单页面跳转过来时）
  selectAddress(e) {
    if (!this.data.fromOrder) {
      return;
    }

    const index = e.currentTarget.dataset.index;
    const selectedAddress = this.data.addressList[index];

    // 将选中的地址信息传回订单页面
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 获取上一个页面

    if (prevPage) {
      prevPage.setData({
        selectedAddress: selectedAddress
      });
    }

    wx.navigateBack();
  },

  // 编辑地址
  editAddr(e) {
    const index = e.currentTarget.dataset.index;
    const addressId = this.data.addressList[index].id;
    tui.href(`../editAddress/editAddress?id=${addressId}`);
  },

  // 添加新地址
  addAddress() {
    tui.href("../editAddress/editAddress");
  },

  // 设置默认地址
  setDefault(e) {
    const index = e.currentTarget.dataset.index;
    const addressList = this.data.addressList.map((item, i) => {
      return {
        ...item,
        isDefault: i === index
      };
    });

    this.setData({
      addressList
    });

    tui.toast('设置成功');
  }
})
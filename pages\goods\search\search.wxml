<view class="container">
		<view class="tui-searchbox">
			<view class="tui-search-input">
				<icon type="search" size='13' color='#333'></icon>
				<input confirm-type="search" placeholder="输入商品信息进行搜索" focus="{{true}}" auto-focus placeholder-class="tui-input-plholder"
				 class="tui-input"  bindinput="inputKey" bindconfirm="searchResultPage"/>
				<icon type="clear" size='13' color='#bcbcbc' bindtap="cleanKey" hidden="{{!key}}"></icon>
			</view>
			<view class="tui-cancle" bindtap="back">取消</view>
		</view>

		<view class="tui-search-history" hidden="{{!(history.length>0 && !key)}}">
			<view class="tui-history-header">
				<view class="tui-search-title">搜索历史</view>
				<tui-icon name="delete" size='14' color='#333' bindtap="openActionSheet" class="tui-icon-delete"></tui-icon>
			</view>
			<view class="tui-history-content">
				<block wx:for="{{history}}" wx:key="index">
					<tui-tag margin="0 24rpx 24rpx 0" type="gray" shape="circle" data-type="{{item}}" bindtap="tagToSearch">{{item}}</tui-tag>
				</block>
			</view>
		</view>
		<view hidden="{{!key}}">
			<view class="tui-header">
				<view class="tui-header-left tui-noboredr">搜索 “{{key}}”</view>
			</view>
			<view class="tui-result-box">
				<block wx:for="{{searchList}}" wx:key="index">
					<view class="tui-result-item" hover-class="tui-opcity" hover-stay-time="150">
						<rich-text nodes="{{item.showKey}}"></rich-text>
					</view>
				</block>
			</view>
		</view>
		<tui-actionsheet show="{{showActionSheet}}" tips="{{tips}}" bindclick="itemClick" bindcancel="closeActionSheet"></tui-actionsheet>
	</view>
page {
  color: #333;
  background: #fff;
}

.container {
  padding: 0 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}

.tui-searchbox {
  padding: 30rpx 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.tui-search-input {
  width: 100%;
  height: 66rpx;
  border-radius: 35rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  background: #f2f2f2;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.tui-input {
  flex: 1;
  color: #333;
  padding: 0 16rpx;
  font-size: 28rpx;
}

.tui-input-plholder {
  font-size: 28rpx;
  color: #b2b2b2;
}

.tui-cancle {
  color: #888;
  font-size: 28rpx;
  padding-left: 30rpx;
  flex-shrink: 0;
}

.tui-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
}

.tui-history-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.tui-icon-delete {
  padding: 10rpx;
}

.tui-search-title {
  font-size: 28rpx;
  font-weight: bold;
}

.tui-hot-header {
  padding: 30rpx 0;
}

.tui-header {
  padding: 26rpx 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tui-header-left {
  font-size: 30rpx;
  color: #222;
  border-left: 4rpx solid #EB0909;
  padding-left: 10rpx;
  word-break: break-all;
}

.tui-noboredr {
  border-left: 0 !important;
}

.tui-result-box {
  font-size: 28rpx;
}

.tui-result-item {
  line-height: 28rpx;
  padding: 28rpx 0;
}
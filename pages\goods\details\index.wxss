page {
  background-color: #f7f7f7;
}

.container {
  padding-bottom: 110rpx;
}

.tui-header-box {
  width: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 995;
}

.tui-header {
  width: 100%;
  font-size: 18px;
  line-height: 18px;
  font-weight: 500;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tui-header-icon {
  position: fixed;
  top: 0;
  left: 10px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  height: 32px;
  transform: translateZ(0);
  z-index: 9999;
}

.tui-header-icon .tui-badge {
  background: #e41f19 !important;
  position: absolute;
  right: -4px;
}

.tui-icon-ml {
  margin-left: 20rpx;
}

.tui-icon-box {
  position: relative;
  height: 20px !important;
  width: 20px !important;
  padding: 6px !important;

  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tui-banner-swiper {
  position: relative;
}

.tui-video__box{
  width: 166rpx;
  height: 60rpx;
  position: absolute;
  left: 50%;
  bottom: 50rpx;
  transform: translateX(-50%);
  z-index: 2;
}

.tui-video__box image{
  width: 166rpx;
  height: 60rpx;
}
.tui-video__box view{
  width: 100%;
  height: 100%;
  font-size: 24rpx;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  padding-left: 66rpx;
  box-sizing: border-box;
}

.tui-banner-tag {
  position: absolute;
  color: #fff;
  bottom: 30rpx;
  right: 0;
}

.tui-slide-image {
  width: 100%;
  display: block;
}

/*顶部菜单*/

.tui-menu-box {
  box-sizing: border-box;
}

.tui-menu-header {
  font-size: 34rpx;
  color: #fff;
  height: 32px;
  display: flex;
  align-items: center;
}

.tui-menu-itembox {
  color: #fff;
  padding: 40rpx 10rpx 0 10rpx;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  font-size: 26rpx;
}

.tui-menu-item {
  width: 22%;
  height: 160rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  margin-right: 4%;
  margin-bottom: 4%;
}

.tui-menu-item:nth-of-type(4n) {
  margin-right: 0;
}

.tui-badge-box {
  position: relative;
}

.tui-badge-box .tui-badge-class {
  position: absolute;
  top: -8px;
  right: -8px;
}

.tui-msg-badge {
  top: -10px;
}

.tui-icon-up_box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tui-menu-text {
  padding-top: 12rpx;
}

.tui-opcity .tui-menu-text,
.tui-opcity .tui-badge-box {
  opacity: 0.5;
  transition: opacity 0.2s ease-in-out;
}

/*顶部菜单*/

/*内容 部分*/

.tui-padding {
  padding: 0 30rpx;
  box-sizing: border-box;
}

.tui-ml-auto {
  margin-left: auto;
}


.tui-size {
  font-size: 24rpx;
  line-height: 24rpx;
}

.tui-gray {
  color: #999;
}

.tui-icon-red {
  color: #ff201f;
}

.tui-border-radius {
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  overflow: hidden;
}

.tui-radius-all {
  border-radius: 24rpx;
  overflow: hidden;
}

.tui-mtop {
  margin-top: 26rpx;
}

.tui-pro-detail {
  box-sizing: border-box;
  color: #333;
}

.tui-product-title {
  background: #fff;
  padding: 30rpx 0;
}

.tui-pro-pricebox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ff201f;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 44rpx;
}

.tui-pro-price {
  display: flex;
  align-items: center;
}

.tui-price {
  font-size: 58rpx;
}

.tui-original-price {
  font-size: 26rpx;
  line-height: 26rpx;
  padding: 10rpx 30rpx;
  box-sizing: border-box;
}

.tui-line-through {
  text-decoration: line-through;
}

.tui-collection {
  color: #333;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 44rpx;
}

.tui-scale-collection {
  transform: scale(0.7);
  transform-origin: center 90%;
  line-height: 24rpx;
  font-weight: normal;
  margin-top: 4rpx;
}

.tui-pro-titbox {
  font-size: 32rpx;
  font-weight: 500;
  position: relative;
  padding: 0 150rpx 0 30rpx;
  box-sizing: border-box;
}

.tui-pro-title {
  padding-top: 20rpx;
}

.tui-share-btn {
  flex: 1;
  display: block;
  background: transparent;
  margin: 0;
  padding: 0;
  border-radius: 0;
  border: 0;
  line-height:1;
}

.tui-share-btn::after {
  border: 0;
}

.tui-share-box {
  display: flex;
  align-items: center;
}

.tui-share-position {
  position: absolute;
  right: 0;
  top: 30rpx;
}

.tui-share-text {
  padding-left: 8rpx;
}

.tui-sub-title {
  padding: 20rpx 0;
  line-height: 32rpx;
}

.tui-sale-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 30rpx;
}

.tui-discount-box {
  background: #fff;
}

.tui-list-cell {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  line-height: 26rpx;
  padding: 36rpx 30rpx;
  box-sizing: border-box;
}

.tui-right {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}

.tui-top40 {
  top: 40rpx !important;
}

.tui-bold {
  font-weight: bold;
}

.tui-list-cell::after {
  content: '';
  position: absolute;
  border-bottom: 1rpx solid #eaeef1;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  bottom: 0;
  right: 0;
  left: 126rpx;
}

.tui-last::after {
  border-bottom: 0 !important;
}

.tui-flex-center {
  display: flex;
  align-items: center;
}


.tui-cell-title {
  width: 66rpx;
  padding-right: 30rpx;
  flex-shrink: 0;
}

.tui-promotion-box {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  width: 80%;
}

.tui-promotion-box text {
  width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tui-basic-info {
  background: #fff;
}

.tui-addr-box {
  width: 76%;
}

.tui-addr-item {
  padding: 10rpx;
  line-height: 34rpx;
}

.tui-guarantee {
  background: #fdfdfd;
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 30rpx 30rpx 30rpx;
  font-size: 24rpx;
}

.tui-guarantee-item {
  color: #999;
  padding-right: 30rpx;
  padding-top: 10rpx;
}

.tui-pl {
  padding-left: 4rpx;
}

.tui-cmt-box {
  background: #fff;
}

.tui-between {
  justify-content: space-between !important;
}

.tui-cmt-all {
  color: #ff201f;
  padding-right: 8rpx;
}

.tui-cmt-content {
  font-size: 26rpx;
}

.tui-cmt-user {
  display: flex;
  align-items: center;
}

.tui-acatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: block;
  margin-right: 16rpx;
}

.tui-cmt {
  padding: 14rpx 0;
}

.tui-attr {
  font-size: 24rpx;
  color: #999;
  padding: 6rpx 0;
}

.tui-cmt-btn {
  padding: 50rpx 0 30rpx 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tui-nomore-box {
  padding-top: 10rpx;
}

.tui-product-img {
  display: flex;
  flex-direction: column;
  transform: translateZ(0);
}

.tui-product-img image {
  width: 100%;
  display: block;
}

/*底部操作栏*/

.tui-col-7 {
  width: 58.33333333%;
}

.tui-col-5 {
  width: 41.66666667%;
}

.tui-operation {
  width: 100%;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.98);
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 10;
  bottom: 0;
  left: 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.tui-safearea-bottom {
  width: 100%;
  height: env(safe-area-inset-bottom);
}

.tui-operation::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  border-top: 1rpx solid #eaeef1;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.tui-operation-left {
  display: flex;
  align-items: center;
}

.tui-operation-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
}

.tui-operation-text {
  font-size: 22rpx;
  color: #333;
}

.tui-opacity {
  opacity: 0.5;
}

.tui-scale-small {
  transform: scale(0.9);
  transform-origin: center center;
}

.tui-operation-right {
  height: 100rpx;
  padding-top: 0;
}

.tui-right-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tui-flex-1 {
  flex: 1;
  padding: 16rpx;
}

/*底部操作栏*/

/*底部选择弹层*/

.tui-popup-class {
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.tui-popup-box {
  position: relative;
  padding: 30rpx 0 100rpx 0;
}

.tui-popup-btn {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
}

.tui-product-box {
  display: flex;
  align-items: flex-end;
  font-size: 24rpx;
  padding-bottom: 30rpx;
}

.tui-popup-img {
  height: 200rpx;
  width: 200rpx;
  border-radius: 24rpx;
  display: block;
}

.tui-popup-price {
  padding-left: 20rpx;
  padding-bottom: 8rpx;
}

.tui-amount {
  color: #ff201f;
  font-size: 36rpx;
}

.tui-number {
  font-size: 24rpx;
  line-height: 24rpx;
  padding-top: 12rpx;
  color: #999;
}

.tui-popup-scroll {
  height: 600rpx;
  font-size: 26rpx;
}

.tui-scrollview-box {
  padding: 0 30rpx 60rpx 30rpx;
  box-sizing: border-box;
}

.tui-attr-title {
  padding: 10rpx 0;
  color: #333;
}

.tui-attr-box {
  font-size: 0;
  padding: 20rpx 0;
}

.tui-attr-item {
  max-width: 100%;
  min-width: 200rpx;
  height: 64rpx;
  display: -webkit-inline-flex;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  padding: 0 26rpx;
  box-sizing: border-box;
  border-radius: 32rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.tui-attr-active {
  background: #fcedea !important;
  color: #e41f19;
  font-weight: bold;
  position: relative;
}

.tui-attr-active::after {
  content: '';
  position: absolute;
  border: 1rpx solid #e41f19;
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
  left: 0;
  top: 0;
}

.tui-number-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0 30rpx 0;
  box-sizing: border-box;
}

/*底部选择弹层*/

/*分享弹层*/
.tui-share__box {
  width: 100%;
  height: 380rpx;
  background-color: #fff;
}

.tui-share__header {
  padding: 40rpx 0;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  position: relative;
}

.tui-close__box {
  position: absolute;
  right: 25rpx;
  top: 25rpx;
}
.tui-share__list{
  width: 100%;
  padding-top: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tui-share__item{
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.tui-share__item image{
  width: 120rpx;
  height: 120rpx;
}
.tui-share__text{
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  padding-top: 18rpx;
}

/*海报modal弹层*/
.tui-poster__canvas {
  background-color: #fff;
  position: absolute;
  left: -9999px;
}

.tui-poster__box {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tui-close__img {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  right: 0;
  top: -60rpx;
}

.tui-poster__img {
  width: 560rpx;
  height: 890rpx;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.tui-share__tips {
  font-size: 24rpx;
  transform: scale(0.8);
  transform-origin: center center;
  color: #ffffff;
  padding-top: 12rpx;
}
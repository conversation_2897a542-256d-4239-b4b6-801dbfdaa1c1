/**
 * 购物车事件管理器
 * 用于在不同页面之间同步购物车数据变化
 */

class CartEventManager {
  constructor() {
    this.listeners = new Map();
  }

  /**
   * 注册购物车数据变化监听器
   * @param {string} pageId 页面唯一标识
   * @param {Function} callback 回调函数
   */
  addListener(pageId, callback) {
    this.listeners.set(pageId, callback);
  }

  /**
   * 移除监听器
   * @param {string} pageId 页面唯一标识
   */
  removeListener(pageId) {
    this.listeners.delete(pageId);
  }

  /**
   * 触发购物车数据变化事件
   * @param {Object} data 变化的数据
   * @param {string} excludePageId 排除的页面ID（通常是触发变化的页面）
   */
  notifyCartChange(data, excludePageId = null) {
    this.listeners.forEach((callback, pageId) => {
      if (pageId !== excludePageId) {
        try {
          callback(data);
        } catch (error) {
          console.error(`购物车事件通知失败 - 页面: ${pageId}`, error);
        }
      }
    });
  }

  /**
   * 获取当前监听器数量
   * @returns {number} 监听器数量
   */
  getListenerCount() {
    return this.listeners.size;
  }

  /**
   * 清空所有监听器
   */
  clearAllListeners() {
    this.listeners.clear();
  }
}

// 创建单例实例
const cartEventManager = new CartEventManager();

module.exports = cartEventManager;

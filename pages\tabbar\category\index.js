import tui from '../../../common/httpRequest'
const cartManager = require('../../../utils/cartManager');
Page({
  api:{
    recommendList: "/pet/recommend/list/all",
    goodsList: "/pet/goods/app/list/all",
    categoryList: "/pet/category/list/all"
  },
  data: {
    webURL: '/static/images/cafe/',
    list: [],
    recommends:[],
    ossUrl:tui.ossUrl(),
    goodsList:[],
    goodsInfo:{},
    height: 20,
    scrollTop: 0.5,
    backgroundColor: '',
    windowHeight: 400,
    menuTop: 250,
    isAndroid: true,
    scrollView_leftId: 'left_0',
    contentTop: {},
    activeTab: 0,
    recalc: 0,
    isTap: false,
    timer: null,
    modal: false,
    total: 0,
    num: 1,
    rackRate: 17, //标准价
    price: 17, //选择规格后价格 
    attrText: '小/热/标准',
    show: false
  },
  onLoad: function () {
    let sys = wx.getWindowInfo()
    this.setData({
      menuTop: sys.windowWidth / 750 * 500,
      isAndroid: tui.isAndroid()
    })
    this.initPageData()
    this.updateCartCount()
  },

  onShow: function() {
    // 页面显示时更新购物车数量
    this.updateCartCount();

    // 注册购物车数据变化监听器
    cartManager.addPageListener('category-page', (data) => {
      this.setData({
        total: data.cartItemCount
      });
    });
  },

  onHide: function() {
    // 页面隐藏时移除监听器
    cartManager.removePageListener('category-page');
  },

  onUnload: function() {
    // 页面卸载时移除监听器
    cartManager.removePageListener('category-page');
  },

  updateCartCount: function() {
    this.setData({
      total: cartManager.getCartItemCount()
    })
  },
  /**
   * 初始化页面数据
   */
  initPageData:function(){
    tui.request(this.api.goodsList,"GET",null,false,false,false).then(res1=>{
      console.log("商品信息：",res1)
      tui.request(this.api.categoryList,"GET",null,false,false,false).then(res2=>{
        console.log("分类信息：",res2)
        // this.getCategoryNameList(res1)
        this.getCategoryNameList(res1,res2.data)
      })
    })
  },
  getCategoryNameList(goodsList,categoryList){

    const grouped = categoryList.reduce((acc, item) => {
      const key = item.name;
      if (!acc[key]) acc[key] = [];
      return acc;
    }, {});
    goodsList.map(goods=>{
      if(grouped.hasOwnProperty(goods.categoryName)){
        grouped[goods.categoryName].push(goods)
      }
    })
    let array = Object.keys(grouped)
    array.unshift("推荐")
    this.getRecommend();
    this.setData({
      goodsList:grouped,
      list: array
    })
  },
  /**
   * 推荐数据
   */
  getRecommend:function(){
    tui.request(this.api.recommendList,"GET",null,false,false,false).then(res=>{
      this.setData({
        recommends: res.data
      })
    })
  },
  getGoodsInfo(e){
    this.setData({
      modal: true
    });
    tui.request(
      "/pet/goods/app/"+e.currentTarget.dataset.type,
      "GET",
      null,
      false,
      false,
      false
    ).then(res=>{
      let current = this.data.current
      this.setData({
        goodsInfo: res.data
      })

    })
  },
  onReady() {
    setTimeout(() => {
      this.setData({
        recalc: 1
      })
    }, 300);
  },
  initNavigation(e) {
    console.log(e)
    this.setData({
      opcity: e.detail.opcity,
      height: e.detail.height,
      windowHeight: e.detail.windowHeight
    })
  },
  opcityChange(e) {
    this.setData({
      opcity: e.detail.opcity
    })
  },
  linkage(e) {
    let index = Number(e.detail.index);
    let value = `contentTop[${index}]`
    this.setData({
      [value]: Number(e.detail.top)
    })
  },
  handleContentScroll(scrollTop) {
    if (scrollTop >= this.data.contentTop[0]) {
      let index = 0;
      for (let i = 0, length = this.data.list.length + 1; i < length; i++) {
        if (scrollTop + this.data.height >= this.data.contentTop[i] && scrollTop + this.data.height < this.data.contentTop[i + 1]) {
          index = i;
          break;
        }
      }
      let id = index - 5 < 0 ? 0 : index - 5;
      this.setData({
        activeTab: index,
        scrollView_leftId: `left_${id}`
      })
    }
  },
  swichNav(e) {
    let cur = Number(e.currentTarget.dataset.index);
    if (this.data.activeTab == cur) {
      return false;
    } else {
      let index = cur - 5 < 0 ? 0 : cur - 5;
      this.setData({
        isTap: true,
        activeTab: cur,
        scrollView_leftId: `left_${index}`
      })
      let scrollTop = this.data.contentTop[cur];
      clearTimeout(this.data.timer);
      this.data.timer = null;
      wx.pageScrollTo({
        scrollTop: scrollTop - this.data.height,
        duration: 100,
        complete: res => {
          this.data.timer = setTimeout(() => {
            this.setData({
              isTap: false
            })
          }, 450);
        }
      });
    }
  },
  selectedAttr(e) {
    let dataset=e.currentTarget.dataset;
    let index=Number(dataset.index);
    let subIndex=Number(dataset.subindex);
    let item = this.data.attr[index];
    let val = `attr[${index}].selected`
    this.setData({
      [val]: item.data[subIndex].id
    })

    //计算价格 & 获取规格
    let price = this.data.rackRate;
    let attr = '';
    this.data.attr.forEach(item => {
      for (let model of item.data) {
        if (item.selected == model.id) {
          price += model.price;
          attr += `${model.name}/`;
          break;
        }
      }
    });
    this.setData({
      attrText: attr.substring(0, attr.length - 1),
      price: price
    })
  },
  add() {
    this.setData({
      num: this.data.num + 1
    })
  },
  reducess() {
    let num = this.data.num - 1;
    this.setData({
      num: num < 1 ? 1 : num
    })
  },
  showModal() {
    this.setData({
      num: 1,
      attrText: '小/热/标准',
      price: this.data.rackRate
    })
    let data = [...this.data.attr]
    data.forEach(item => {
      item.selected = 1;
    });

    this.setData({
      attr: data,
      modal: true
    })
  },
  hideModel() {
    this.setData({
      modal: false
    })
  },
  addCart(e) {
    // 检查是否有选中的商品信息
    if (!this.data.goodsInfo || !this.data.goodsInfo.id) {
      tui.toast("请先选择商品！");
      return;
    }

    let type = e.currentTarget.dataset.type;
    let quantity = type == 1 ? 1 : this.data.num;

    // 使用购物车管理器添加商品
    const result = cartManager.addToCart(this.data.goodsInfo, quantity);

    if (result.success) {
      // 更新本地购物车数量显示
      this.setData({
        total: cartManager.getCartItemCount()
      });

      // 显示成功提示
      tui.toast(result.message);

      // 隐藏弹窗
      this.hideModel();
    } else {
      // 显示错误提示
      tui.toast(result.message);
    }
  },
  popupShow() {
    // 获取购物车数据并显示弹层
    const cartItems = cartManager.getCartItems();
    this.setData({
      show: !this.data.show,
      cartItems: cartItems,
      cartTotalPrice: cartManager.getTotalPrice()
    })
  },
  popupHide(){
    this.setData({
      show: false
    })
  },

  // 清空购物车
  clearCartItems() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物袋吗？',
      success: (res) => {
        if (res.confirm) {
          const result = cartManager.clearCart();
          if (result.success) {
            this.setData({
              total: 0,
              cartItems: [],
              cartTotalPrice: 0,
              show: false
            });
            tui.toast(result.message);
          }
        }
      }
    });
  },

  // 更新购物车商品数量
  updateCartItemQuantity(e) {
    const itemId = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type; // 'plus' 或 'minus'
    const cartItems = cartManager.getCartItems();
    const item = cartItems.find(item => item.id === itemId);

    if (item) {
      let newQuantity = item.num;
      if (type === 'plus') {
        newQuantity += 1;
      } else if (type === 'minus') {
        newQuantity -= 1;
      }

      if (newQuantity <= 0) {
        // 删除商品
        cartManager.removeFromCart(itemId);
      } else {
        // 更新数量
        cartManager.updateQuantity(itemId, newQuantity);
      }

      // 更新显示
      this.setData({
        total: cartManager.getCartItemCount(),
        cartItems: cartManager.getCartItems(),
        cartTotalPrice: cartManager.getTotalPrice()
      });
    }
  },
  login() {
    tui.href('../login/login')
  },
  submitOrder() {
    const selectedItems = cartManager.getSelectedItems();

    if (selectedItems.length === 0) {
      tui.toast('购物袋是空的，请先添加商品');
      return;
    }

    wx.navigateTo({
      url: '/pages/submitOrder/submitOrder'
    });
  },
  bannerChange: function(e) {
    let current=e.detail.current
    this.setData({
      current:current
    })
  },
  toGoodsDetails(e){
    wx.navigateTo({
      url: '/pages/goods/details/index?id='+e.currentTarget.dataset.type,
    })
  },
  onPageScroll(e) {
    this.setData({
      scrollTop: e.scrollTop
    })
    if (this.data.isTap) return;
    this.handleContentScroll(e.scrollTop);
  },
  onShareAppMessage: function (e) {
    return {
      title: '点晶宠物批发平台'
    };
  }
})
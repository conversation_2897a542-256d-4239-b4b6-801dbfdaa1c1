Component({
  properties: {
    scrollTop: {
      type: Number,
      value: 0
    },
    recalc: {
      type: Number,
      value: 0,
      observer(val) {
        this.updateScrollChange();
      }
    },
    //列表中的索引值
    index: {
      type: [Number, String],
      value: 0
    }
  },
  data: {
    timer:null
  },
  lifetimes: {
    ready: function () {
      setTimeout(() => {
        this.updateScrollChange();
      }, 20);
    }
  },
  methods: {
    updateScrollChange() {
      if (this.data.timer) {
        clearTimeout(this.data.timer);
        this.setData({
          timer:null
        })
      }
      this.data.timer = setTimeout(() => {
        const className = '.tui-linkage-class';
        const query = wx.createSelectorQuery().in(this);
        query
          .select(className)
          .boundingClientRect(res => {
            if (res) {
              let top = res.top + this.data.scrollTop;
              this.triggerEvent('linkage', {
                top: top,
                height: res.height,
                index: this.data.index
              })
            }
          })
          .exec();
      }, 0);
    }
  }
})
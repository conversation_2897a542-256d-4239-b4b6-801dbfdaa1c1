/**
 * 常用方法封装 请求，文件上传等
 * <AUTHOR> 
 **/

const tui = {
	//接口地址
	interfaceUrl: function() {
    return 'https://pet.erp.starbounce.cn/prod-api'
    // return 'http://zzd.frp.starbounce.cn/prod-api'
    // return 'http://localhost:8688'
  },
  tenantId:"723277",//点晶
  // tenantId:"000000",
  ossUrl: function() {
    return 'https://oss.starbounce.cn/'
	},
	toast: function(text) {
		wx.showToast({
			title: text || "出错啦~",
			icon: 'none',
			duration: 2000
		})
	},
	modal: function(title, content, showCancel, callback, confirmColor, confirmText) {
    wx.showModal({
			title: title || '提示',
			content: content,
			showCancel: showCancel,
			cancelColor: "#555",
			confirmColor: confirmColor || "#5677fc",
			confirmText: confirmText || "确定",
			success(res) {
				if (res.confirm) {
					callback && callback(true)
				} else {
					callback && callback(false)
				}
			}
		})
	},
  copy(data,msg){
    wx.setClipboardData({
      data: data,
      success(res) {
        wx.getClipboardData({
          success(res) {
            msg && tui.toast(msg)
          }
        })
      }
    })
  },
	isAndroid: function() {
		const res = wx.getSystemInfoSync();
		return res.platform.toLocaleLowerCase() == "android"
	},
	constNum: function() {
		let time = 0;
		time = this.isAndroid() ? 300 : 0;
		return time
	},
	delayed: null,
	showLoading: function(title, mask = true) {
		wx.showLoading({
			mask: mask,
			title: title || '网络数据加载中...'
		})
	},
	/**
	 * 请求数据处理
	 * @param string url 请求地址
	 * @param string method 请求方式
	 *  GET or POST
	 * @param {*} postData 请求参数
	 * @param bool isDelay 是否延迟显示loading
	 * @param bool isForm 数据格式
	 *  true: 'application/x-www-form-urlencoded'
	 *  false:'application/json'
	 * @param bool hideLoading 是否隐藏loading
	 *  true: 隐藏
	 *  false:显示
	 */
	request: function(url, method, postData, isDelay, isForm, hideLoading) {
		//接口请求
		let loadding = false;
		tui.delayed && wx.hideLoading();
		clearTimeout(tui.delayed);
		tui.delayed = null;
		if (!hideLoading) {
			if (isDelay) {
				tui.delayed = setTimeout(() => {
					loadding = true
					tui.showLoading()
				}, 1000)
			} else {
				loadding = true
				tui.showLoading()
			}
		}

		return new Promise((resolve, reject) => {
			wx.request({
				url: tui.interfaceUrl() + url,
				data: postData,
				header: {
					'content-type': isForm ? 'application/x-www-form-urlencoded' : 'application/json',
          'Authorization': tui.getToken(),
          "clientid": 'e5cd7e4891bf95d1d19206ce24a7b32e',
          "isEncrypt": false
				},
				method: method, //'GET','POST'
				dataType: 'json',
				success: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					if (loadding && !hideLoading) {
						wx.hideLoading()
          }
          // if(res.data && res.data.code == 500){
          //   tui.toast(res.msg)
          // }
					if (res.data && res.data.code == 401) {
            // wx.reLaunch({
            //   url: '/pages/login/login'
            // })
            // return
            wx.login({
              success (res) {
                if (res.code) {
                  tui.request(
                    "/app/auth/login",
                    "POST",
                    {
                      "code":res.code,
                      "phoneCode":null,
                      "clientId": "e5cd7e4891bf95d1d19206ce24a7b32e",
                      "tenantId": tui.tenantId,
                      "grantType": "PurchaseApp"
                    },
                    false,
                    false,
                    false
                  ).then(res1=>{
                    if(res1.code == 200){
                      wx.setStorageSync('auth_token', res1.data.access_token)
                      wx.setStorageSync('nickname', res1.data.nickname)
                      wx.switchTab({
                        url: '/pages/tabbar/home/<USER>',
                      })
                    }
                  })
                }
              }
            })
          }
          wx.hideLoading()
					resolve(res.data)
				},
				fail: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					tui.toast("网络不给力，请稍后再试~")
					reject(res)
				}
			})
		})
	},
	/**
	 * 上传文件
	 * @param string url 请求地址
	 * @param string src 文件路径
	 */
	uploadFile: function(url, src) {
		tui.showLoading()
		return new Promise((resolve, reject) => {
			const uploadTask = wx.uploadFile({
				url: tui.interfaceUrl() + url,
				filePath: src,
				name: 'imageFile',
				header: {
					'Authorization': tui.getToken()
				},
				formData: {
					// sizeArrayText:""
				},
				success: function(res) {
					wx.hideLoading()
					let d = JSON.parse(res.data.replace(/\ufeff/g, "") || "{}")
					if (d.code % 100 == 0) {
						//返回图片地址
						let fileObj = d.data;
						resolve(fileObj)
					} else {
						that.toast(res.msg);
					}
				},
				fail: function(res) {
					reject(res)
					that.toast(res.msg);
				}
			})
		})
	},
	//设置用户信息
	setUserInfo: function(mobile,token) {
		//wx.setStorageSync("thorui_token", token)
		wx.setStorageSync("thorui_mobile", mobile)
	},
	//获取token
	getToken() {
		return 'Bearer '+wx.getStorageSync("auth_token")
	},
	//判断是否登录
	isLogin: function() {
		return wx.getStorageSync("thorui_mobile") ? true : false
	},
	//跳转页面，校验登录状态
	href(url, isVerify) {
		if (isVerify && !tui.isLogin()) {
			wx.navigateTo({
				url: '/pages/common/login/login'
			})
		} else {
			wx.navigateTo({
				url: url
			});
		}
  },
  rpx2px(value){
		let sys=wx.getSystemInfoSync()
		return sys.windowWidth / 750 * value
	}
}

export default tui
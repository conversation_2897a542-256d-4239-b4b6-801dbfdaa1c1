.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.cart-info {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.price {
  color: #e41f19;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #fff;
  border: none;
}

.add-btn {
  background-color: #07c160;
}

.update-btn {
  background-color: #576b95;
}

.remove-btn {
  background-color: #fa5151;
}

.select-btn {
  background-color: #ff9500;
}

.unselect-btn {
  background-color: #c9c9c9;
  color: #666;
}

.clear-btn {
  background-color: #e64340;
}

.cart-btn {
  background-color: #1aad19;
}

.cart-items {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.items-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.items-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.item:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.item-price {
  font-size: 26rpx;
  color: #e41f19;
}

.item-quantity {
  font-size: 24rpx;
  color: #666;
}

.item-selected {
  font-size: 24rpx;
  color: #07c160;
}

.empty-cart {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

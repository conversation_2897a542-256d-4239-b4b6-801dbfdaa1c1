const { default: tui } = require("../../../common/httpRequest");
const util = require("../../../utils/util")
Page({
  data: {
    history: [],
    hot: [],
    key: "",
    showActionSheet: false,
    tips: "确认清空搜索历史吗？",
    searchResult:[],
    searchList:[]
  },
  onLoad:function(){
    this.setData({
      history: wx.getStorageSync('historySearch')
    })
  },
  back: function() {
    wx.navigateBack();
  },
  cleanKey: function() {
    this.setData({
      key:''
    })
  },
  closeActionSheet: function() {
    this.setData({
      showActionSheet:false
    })
  },
  openActionSheet: function() {
    this.setData({
      showActionSheet:true
    })
  },
  itemClick: function(e) {
    let index = e.detail.index;
    if (index == 0) {
      wx.setStorageSync('historySearch', [])
      this.setData({
        showActionSheet:false,
        history:[]
      })
    }
  },
  inputKey: function(e) {
    let val=e.detail.value
    this.setData({
      key:util.trim(val)
    })
  },
  searchResultPage:function(event){
    let historySearch = wx.getStorageSync('historySearch') || []
    historySearch.push(this.data.key)
    wx.setStorageSync('historySearch', [...new Set(historySearch)])
    wx.navigateTo({
      url: '/pages/goods/list/index?key='+this.data.key,
    })
  },
  tagToSearch:function(e){
    wx.navigateTo({
      url: '/pages/goods/list/index?key='+e.currentTarget.dataset.type,
    })
  }
})
<view class="cart-badge-container">
  <slot></slot>
  <view 
    class="cart-badge cart-badge-{{position}}" 
    wx:if="{{cartCount > 0 || showZero}}"
    style="background-color: {{color}}; color: {{textColor}}; width: {{size}}rpx; height: {{size}}rpx; line-height: {{size}}rpx; font-size: {{size * 0.6}}rpx;"
    bindtap="onBadgeClick"
  >
    <wxs module="utils">
      module.exports = {
        formatCount: function(count, maxCount, showZero) {
          if (count <= 0 && !showZero) {
            return '';
          }
          if (count > maxCount) {
            return maxCount + '+';
          }
          return count.toString();
        }
      }
    </wxs>
    {{utils.formatCount(cartCount, maxCount, showZero)}}
  </view>
</view>

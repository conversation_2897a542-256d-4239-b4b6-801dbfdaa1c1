.tui-drawer-mask {
	opacity: 0;
	visibility: hidden;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out;
}
.tui-drawer-mask_show {
	display: block;
	visibility: visible;
	opacity: 1;
}

.tui-drawer-container {
	position: fixed;
	left: 50%;
	height: 100.2%;
	top: 0;
	transform: translate3d(-50%, -50%, 0);
	transform-origin: center;
	transition: all 0.3s ease-in-out;
	opacity: 0;
	overflow-y: scroll;
	-webkit-overflow-scrolling: touch;
}
.tui-drawer-container_left {
	left: 0;
	top: 50%;
	transform: translate3d(-100%, -50%, 0);
}

.tui-drawer-container_right {
	right: 0;
	top: 50%;
	left: auto;
	transform: translate3d(100%, -50%, 0);
}

.tui-drawer-container_bottom,
.tui-drawer-container_top {
	width: 100%;
	height: auto !important;
	min-height: 20rpx;
	left: 0;
	right: 0;
	transform-origin: center;
	transition: all 0.3s ease-in-out;
}
.tui-drawer-container_bottom {
	bottom: 0;
	top: auto;
	transform: translate3d(0, 100%, 0);
}
.tui-drawer-container_top {
	transform: translate3d(0, -100%, 0);
}
.tui-drawer-left__show,
.tui-drawer-right__show {
	opacity: 1;
	transform: translate3d(0, -50%, 0);
}
.tui-drawer-top__show,
.tui-drawer-bottom__show {
	opacity: 1;
	transform: translate3d(0, 0, 0);
}
<view class="container">
		<view class="tui-order__detail">
			<view class="tui-order__info">
				<view class="tui-logo__box"><image src="{{webURL}}logo.png" mode="widthFix"></image></view>
				<radio-group bindchange="switchShipping">
					<label class="tui-shipping">
						<radio class="tui-radio" color="#ac9157" value="2" checked="{{way==2}}"></radio>
						<text>快递</text>
					</label>
					<tui-list-cell padding="0" arrow unlined hover="{{false}}" wx:if="{{way==2}}" bindclick="selectAddr">
						<view class="tui-input__box"><input disabled type="text" class="tui-input" placeholder-class="tui-placeholder" placeholder="请选择收货地址" /></view>
					</tui-list-cell>
				</radio-group>
				<tui-list-cell padding="0" arrow unlined hover="{{false}}" wx:if="{{way==3}}">
					<view class="tui-input__box"><input disabled type="text" class="tui-input" placeholder-class="tui-placeholder" placeholder="请选择预约时间" /></view>
				</tui-list-cell>
				<input type="text" class="tui-input"  placeholder-class="tui-placeholder" placeholder="备注..." />
				<view class="tui-divider"></view>
			</view>
			<view class="tui-detail__box">
				<view class="tui-goods__list">
					<view class="tui-total__box">
						<view>合计：￥59.00</view>
						<view>共4件商品</view>
					</view>

				</view>
			</view>
		</view>
		<view class="tui-tabbar">
			<view class="tui-total__price">合计：￥58.80</view>
			<view class="tui-btn__box">
				<text>积分：-￥0.20</text>
				<tui-button type="brown" width="180rpx" height="60rpx" size="26" shape="circle">付款</tui-button>
			</view>
		</view>
	</view>
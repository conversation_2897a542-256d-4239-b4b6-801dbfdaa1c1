<view class="container">
		<view class="tui-order__detail">
			<!-- 配送信息 -->
			<view class="tui-order__info">
				<view class="tui-logo__box"><image src="{{webURL}}logo.png" mode="widthFix"></image></view>
				<radio-group bindchange="switchShipping">
					<label class="tui-shipping">
						<radio class="tui-radio" color="#ac9157" value="2" checked="{{way==2}}"></radio>
						<text>快递配送</text>
						<text class="tui-shipping-fee" wx:if="{{way==2}}">{{shippingFee > 0 ? '￥' + shippingFee : '免运费'}}</text>
					</label>
					<label class="tui-shipping">
						<radio class="tui-radio" color="#ac9157" value="3" checked="{{way==3}}"></radio>
						<text>到店自提</text>
						<text class="tui-shipping-fee">免费</text>
					</label>
				</radio-group>

				<!-- 收货地址选择 -->
				<tui-list-cell padding="0" arrow unlined hover="{{false}}" wx:if="{{way==2}}" bindclick="selectAddr">
					<view class="tui-input__box">
						<input disabled type="text" class="tui-input" placeholder-class="tui-placeholder"
							   placeholder="请选择收货地址" value="{{selectedAddress ? selectedAddress.fullAddress : ''}}" />
					</view>
				</tui-list-cell>

				<!-- 自提时间选择 -->
				<tui-list-cell padding="0" arrow unlined hover="{{false}}" wx:if="{{way==3}}">
					<view class="tui-input__box">
						<input disabled type="text" class="tui-input" placeholder-class="tui-placeholder" placeholder="请选择预约时间" />
					</view>
				</tui-list-cell>

				<!-- 备注 -->
				<input type="text" class="tui-input" placeholder-class="tui-placeholder"
					   placeholder="备注..." bindinput="onRemarkInput" value="{{remark}}" />
				<view class="tui-divider"></view>
			</view>

			<!-- 商品列表 -->
			<view class="tui-detail__box">
				<view class="tui-goods__list">
					<view class="tui-goods__header">
						<text class="tui-header__title">商品清单</text>
					</view>

					<!-- 商品项 -->
					<view class="tui-goods__item" wx:for="{{selectedItems}}" wx:key="id">
						<image src="{{ossUrl + item.url}}" class="tui-goods__img" mode="aspectFill"></image>
						<view class="tui-goods__info">
							<view class="tui-goods__name">{{item.goodsName}}</view>
							<view class="tui-goods__price">￥{{item.wholesalePrice}}</view>
						</view>
						<view class="tui-goods__quantity">x{{item.num}}</view>
						<view class="tui-goods__subtotal">￥{{(item.wholesalePrice * item.num).toFixed(2)}}</view>
					</view>

					<!-- 价格汇总 -->
					<view class="tui-price__summary">
						<view class="tui-price__item">
							<text class="tui-price__label">商品小计</text>
							<text class="tui-price__value">￥{{totalPrice.toFixed(2)}}</text>
						</view>
						<view class="tui-price__item" wx:if="{{shippingFee > 0}}">
							<text class="tui-price__label">运费</text>
							<text class="tui-price__value">￥{{shippingFee.toFixed(2)}}</text>
						</view>
						<view class="tui-price__item" wx:if="{{discountAmount > 0}}">
							<text class="tui-price__label">积分抵扣</text>
							<text class="tui-price__value tui-discount">-￥{{discountAmount.toFixed(2)}}</text>
						</view>
					</view>

					<view class="tui-total__box">
						<view class="tui-total__price">合计：￥{{finalPrice.toFixed(2)}}</view>
						<view class="tui-total__count">共{{totalQuantity}}件商品</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部结算栏 -->
		<view class="tui-tabbar">
			<view class="tui-total__info">
				<view class="tui-total__price">实付：￥{{finalPrice.toFixed(2)}}</view>
				<view class="tui-total__detail" wx:if="{{discountAmount > 0}}">
					<text class="tui-discount__text">积分抵扣：-￥{{discountAmount.toFixed(2)}}</text>
				</view>
			</view>
			<view class="tui-btn__box">
				<tui-button type="brown" width="180rpx" height="60rpx" size="26" shape="circle" bindclick="submitOrder">
					立即支付
				</tui-button>
			</view>
		</view>
	</view>
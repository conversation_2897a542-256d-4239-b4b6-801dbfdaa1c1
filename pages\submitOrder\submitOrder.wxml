<view class="container">
		<view class="tui-order__detail">
			<!-- 配送信息 -->
			<view class="tui-order__info">
				<view class="tui-logo__box"><image src="{{webURL}}logo.png" mode="widthFix"></image></view>
				<view class="tui-shipping-info">
					<text class="tui-shipping-label">配送方式：快递配送</text>
					<text class="tui-shipping-fee">{{shippingFee > 0 ? '￥' + shippingFee : '免运费'}}</text>
				</view>

				<!-- 收货地址选择 -->
				<tui-list-cell padding="0" arrow unlined hover="{{false}}" bindclick="selectAddr">
					<view class="tui-input__box">
						<input disabled type="text" class="tui-input" placeholder-class="tui-placeholder"
							   placeholder="请选择收货地址" value="{{selectedAddress ? selectedAddress.fullAddress : ''}}" />
					</view>
				</tui-list-cell>

				<!-- 备注 -->
				<input type="text" class="tui-input" placeholder-class="tui-placeholder"
					   placeholder="备注..." bindinput="onRemarkInput" value="{{remark}}" />
				<view class="tui-divider"></view>
			</view>

			<!-- 商品列表 -->
			<view class="tui-detail__box">
				<view class="tui-goods__list">
					<view class="tui-goods__header">
						<text class="tui-header__title">商品清单</text>
					</view>

					<!-- 商品项 -->
					<view class="tui-goods__item" wx:for="{{selectedItems}}" wx:key="id">
						<image src="{{ossUrl + item.url}}" class="tui-goods__img" mode="aspectFill"></image>
						<view class="tui-goods__info">
							<view class="tui-goods__name">{{item.goodsName}}</view>
							<view class="tui-goods__price">￥{{item.wholesalePrice}}</view>
						</view>
						<view class="tui-goods__quantity">x{{item.num}}</view>
						<view class="tui-goods__subtotal">￥{{utils.formatPrice(item.wholesalePrice * item.num)}}</view>
					</view>

					<!-- 价格汇总 -->
					<view class="tui-price__summary">
						<view class="tui-price__item">
							<text class="tui-price__label">商品小计</text>
							<text class="tui-price__value">￥{{utils.formatPrice(totalPrice)}}</text>
						</view>
						<view class="tui-price__item" wx:if="{{shippingFee > 0}}">
							<text class="tui-price__label">运费</text>
							<text class="tui-price__value">￥{{utils.formatPrice(shippingFee)}}</text>
						</view>
					</view>

					<view class="tui-total__box">
						<view class="tui-total__price">合计：￥{{utils.formatPrice(finalPrice)}}</view>
						<view class="tui-total__count">共{{totalQuantity}}件商品</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部结算栏 -->
		<view class="tui-tabbar">
			<view class="tui-total__info">
				<view class="tui-total__price">实付：￥{{utils.formatPrice(finalPrice)}}</view>
			</view>
			<view class="tui-btn__box">
				<tui-button type="brown" width="180rpx" height="60rpx" size="26" shape="circle" bindclick="submitOrder">
					立即支付
				</tui-button>
			</view>
		</view>
	</view>

<wxs module="utils">
  module.exports = {
    formatPrice: function(price) {
      price = price || 0;
      return price.toFixed(2);
    }
  }
</wxs>
.container {
	width: 100%;
	padding: 10rpx 30rpx 200rpx;
	box-sizing: border-box;
}

.tui-order__detail {
	width: 100%;
	border-radius: 12rpx;
	overflow: hidden;
	background-color: #fff;
	box-shadow: 0 3rpx 20rpx rgba(183, 183, 183, 0.1);
}

.tui-order__info {
	width: 100%;
	padding: 50rpx 50rpx 80rpx;
	box-sizing: border-box;
	position: relative;
}
.tui-logo__box {
	display: flex;
	align-items: center;
	padding-bottom: 40rpx;
	font-size: 36rpx;
	font-weight: bold;
}
.tui-logo__box image {
	width: 184rpx;
	height: 60rpx;
}
.tui-shipping-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	font-size: 30rpx;
	font-weight: bold;
}

.tui-shipping-label {
	color: #333;
}

.tui-shipping-fee {
	font-size: 24rpx;
	color: #ac9157;
	font-weight: normal;
}
.tui-input {
	width: 100%;
	height: 80rpx;
	padding: 20rpx;
	border-radius: 8rpx;
	font-size: 26rpx;
	box-sizing: border-box;
	background-color: #f8f8f8;
	margin-top: 20rpx;
}
.tui-placeholder {
	color: #bbbbbb;
}
.tui-divider {
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 0;
	border-bottom: 1px dashed #eee;
}

.tui-divider::before {
	content: ' ';
	position: absolute;
	left: 0;
	top: -50%;
	width: 28rpx;
	height: 28rpx;
	border-radius: 50%;
	background-color: #f5f5f5;
	transform: translate(-50%, -50%);
	z-index: 10;
}

.tui-divider::after {
	content: ' ';
	position: absolute;
	right: 0;
	top: -50%;
	width: 28rpx;
	height: 28rpx;
	border-radius: 50%;
	background-color: #f5f5f5;
	transform: translate(50%, -50%);
	z-index: 10;
}

.tui-detail__box {
	width: 100%;
	padding: 50rpx;
	box-sizing: border-box;
}

/* 商品列表样式 */
.tui-goods__header {
	padding-bottom: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	margin-bottom: 30rpx;
}

.tui-header__title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.tui-goods__item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f8f8f8;
}

.tui-goods__item:last-child {
	border-bottom: none;
}

.tui-goods__img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 12rpx;
	flex-shrink: 0;
}

.tui-goods__info {
	flex: 1;
	padding: 0 20rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.tui-goods__name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.tui-goods__price {
	font-size: 26rpx;
	color: #e41f19;
	font-weight: bold;
}

.tui-goods__quantity {
	font-size: 26rpx;
	color: #666;
	margin-right: 20rpx;
}

.tui-goods__subtotal {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 价格汇总样式 */
.tui-price__summary {
	margin: 30rpx 0;
	padding: 20rpx 0;
	border-top: 1rpx solid #f0f0f0;
}

.tui-price__item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
}

.tui-price__label {
	font-size: 28rpx;
	color: #666;
}

.tui-price__value {
	font-size: 28rpx;
	color: #333;
}



.tui-total__box {
	width: 100%;
	font-size: 30rpx;
	line-height: 30rpx;
	padding: 20rpx 0;
	font-weight: bold;
	display: flex;
	justify-content: space-between;
	border-top: 2rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.tui-total__price {
	color: #e41f19;
}

.tui-total__count {
	color: #666;
	font-size: 26rpx;
	font-weight: normal;
}

.tui-goods__item {
	width: 100%;
	display: flex;
	align-items: center;
	position: relative;
}

.tui-goods__item image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	background-color: #f8f8f8;
	flex-shrink: 0;
}

.tui-goods__info {
	width: 60%;
	font-size: 26rpx;
	padding-left: 20rpx;
}

.tui-name {
	width: 100%;
	font-size: 28rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.tui-attr {
	font-size: 24rpx;
	color: #999;
	padding: 8rpx 0;
}

.tui-quantity {
	position: absolute;
	right: 0;
	top: 8rpx;
	font-size: 24rpx;
}
.tui-tabbar {
	width: 100%;
	height: 120rpx;
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 10;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 1px 1px 10px 1px rgba(0, 0, 0, 0.2);
	background-color: #fff;
	padding: 0 30rpx;
	box-sizing: border-box;
}

.tui-total__info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.tui-total__info .tui-total__price {
	font-size: 32rpx;
	font-weight: bold;
	color: #e41f19;
	margin-bottom: 5rpx;
}



.tui-btn__box{
	display: flex;
	align-items: center;
}
.tui-input__box {
	margin-bottom: 20rpx;
}
.tui-input__box .tui-input {
	padding-right: 60rpx;
}
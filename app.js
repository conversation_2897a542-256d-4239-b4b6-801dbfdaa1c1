//app.js
import tui from './common/httpRequest'
App({
  onLaunch: function () {
    let authToken = wx.getStorageSync('auth_token')
    if(authToken != undefined && authToken != ''){
      tui.request(
        "/pet/customer/getUserinfo",
        "GET",
        {},
        false,
        false,
        false
      ).then(res2=>{
        if(res2.code != 200){

        } else {
          wx.switchTab({
            url: '/pages/tabbar/home/<USER>',
          })
        } 
      })
    }

  },
  onShow:function(){

  },
  globalData: {
    
  }
})
<view class="container">
		<!--header-->
		<view class="tui-header">
			<view class="tui-rolling-search" bindtap="toSearchPage">
				<tui-icon name="search-2" size="32" unit="rpx"></tui-icon>
			</view>
		</view>
		<!--header-->
		<view class="tui-header-banner">
			<view class="tui-banner-bg">
				<view class="tui-primary-bg tui-route-left"></view>
				<view class="tui-primary-bg tui-route-right"></view>
				<!--banner-->
				<view class="tui-banner-box">
					<swiper
						indicator-dots="{{true}}"
						autoplay="{{true}}"
						interval="5000"
						duration="150"
						class="tui-banner-swiper"
						circular="{{true}}"
						indicator-color="rgba(255, 255, 255, 0.8)"
						indicator-active-color="#fff"
					>
						<swiper-item wx:for="{{banner}}" wx:key="index"  catchtap="detail">
							<image src="{{ossUrl + item.imageUrl}}" class="tui-slide-image" mode="scaleToFill" />
						</swiper-item>
					</swiper>
				</view>
			</view>
		</view>

		<view class="tui-product-category">
			<view class="tui-category-item" wx:for="{{category}}" wx:key="index" data-key="{{item.name}}"  bindtap="toGoodsListPage">
				<image src="/static/images/home/<USER>" class="tui-category-img" mode="scaleToFill"></image>
				<view class="tui-category-name">{{ item.name }}</view>
			</view>
		</view>
		<image src="https://thorui.cn/images/mall/activity/img_coupon_banner.png" class="tui-img__coupon"></image>
		<view class="tui-product-box">
			<!--排行榜-->
			<view class="tui-block__box tui-mtop__20">
				<view class="tui-group-name" bindtap="more">
					<view>
						<text>排行榜</text>
						<text class="tui-sub__desc">大家都在买</text>
					</view>
					<view class="tui-more__box">
						<!-- <text>更多</text>
						<tui-icon name="arrowright" size="{{36}}" unit="rpx" color="#999"></tui-icon> -->
					</view>
				</view>
				<view class="tui-new-box">
					<view class="tui-new-item {{index != 0 && index != 1 ? 'tui-new-mtop' : ''}}" wx:for="{{topGoodsList}}" wx:key="index" data-type="{{item.id}}" bindtap="toGoodsDetails" >
          <!-- 角标 -->
						<image src="/static/images/home/<USER>" class="tui-new-label" />
						<view class="tui-title-box" >
							<view class="tui-new-title">{{ item.goodsName }}</view>
							<view class="tui-new-price">
								<text class="tui-new-present">￥{{ item.wholesalePrice }}</text>
								<text class="tui-new-original">￥{{ item.retailPrice }}</text>
							</view>
						</view>
						<image src="{{ossUrl + item.whiteBasedImage}}" class="tui-new-img"></image>
					</view>
				</view>
			</view>
		</view>

		<view class="tui-product-box">
			<view class="tui-title__img">
				<image src="https://thorui.cn/images/mall/img_home_update_3x.png" mode="widthFix"></image>
			</view>
			<view class="tui-product-list">
				<view class="tui-product-container">
					<block wx:for="{{productList}}" wx:key="index" wx:for-item="goods"  wx:if="{{(index + 1) % 2 != 0}}">
						<!--商品列表-->
						<view class="tui-pro-item" hover-class="hover" hover-start-time="150"  data-type="{{goods.id}}" bindtap="toGoodsDetails" >
							<image src="{{ossUrl + goods.masterImg}}" class="tui-pro-img" mode="widthFix" />
							<view class="tui-pro-content">
								<view class="tui-pro-tit">{{ goods.goodsName }}</view>
								<view>
									<view class="tui-pro-price">
										<text class="tui-sale-price">￥{{ goods.wholesalePrice }}</text>
										<text class="tui-factory-price">￥{{ goods.retailPrice }}</text>
									</view>
									<view class="tui-pro-pay">本月{{ goods.salesVolume }}人付款</view>
								</view>
							</view>
						</view>
						<!--商品列表-->
					</block>
				</view>

				<view class="tui-product-container">
					<block wx:for="{{productList}}" wx:key="index" wx:for-item="goods" wx:if="{{(index + 1) % 2 == 0}}">
						<view class="tui-pro-item" hover-class="hover" hover-start-time="150"  data-type="{{goods.id}}" bindtap="toGoodsDetails" >
							<image src="{{ossUrl + goods.masterImg}}" class="tui-pro-img" mode="widthFix" />
							<view class="tui-pro-content">
								<view class="tui-pro-tit">{{ goods.goodsName }}</view>
								<view>
									<view class="tui-pro-price">
										<text class="tui-sale-price">￥{{ goods.wholesalePrice }}</text>
										<text class="tui-factory-price">￥{{ goods.retailPrice }}</text>
									</view>
									<view class="tui-pro-pay">本月{{ goods.salesVolume }}人付款</view>
								</view>
							</view>
						</view>
					</block>
				</view>
        
			</view>
		</view>

		<!--加载loadding-->
		<tui-loadmore wx:if="{{loadding}}" index="3" type="red"></tui-loadmore>
		<!--加载loadding-->
		<view class="tui-safearea-bottom"></view>
	</view>
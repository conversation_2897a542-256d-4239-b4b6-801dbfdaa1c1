.tui-header-box {
  width: 100%;
  padding: 0 30rpx 0 20rpx;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 32px;
  transform: translateZ(0);
  z-index: 9999;
}

.tui-set-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tui-icon-box {
  position: relative;
}

.tui-icon-setup {
  margin-left: 8rpx;
}

.tui-badge {
  position: absolute;
  font-size: 24rpx;
  height: 32rpx;
  min-width: 20rpx;
  padding: 0 6rpx;
  border-radius: 40rpx;
  right: 10rpx;
  top: -5rpx;
  transform: scale(0.8) translateX(60%);
  transform-origin: center center;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.tui-badge-red {
  background: #F74D54;
  color: #fff;
}

.tui-badge-white {
  background: #fff;
  color: #F74D54;
}

.tui-badge-dot {
  position: absolute;
  height: 12rpx;
  width: 12rpx;
  border-radius: 50%;
  right: -12rpx;
  top: 0;
  background: #F74D54;
}

.tui-mybg-box {
  width: 100%;
  height: 464rpx;
  position: relative;
}

.tui-my-bg {
  width: 100%;
  height: 464rpx;
  display: block;
}

.tui-header-center {
  position: absolute;
  width: 100%;
  height: 128rpx;
  left: 0;
  top: 120rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.tui-avatar {
  flex-shrink: 0;
  width: 128rpx;
  height: 128rpx;
  display: block;
}

.tui-info {
  width: 60%;
  padding-left: 30rpx;

}
.tui-login {
  width: 60%;
  padding-left: 30rpx;
  font-size: 32rpx;
  line-height: 32rpx;
  color: #fff;
  display: flex;
  align-items: center;
}

.tui-nickname {
  font-size: 30rpx;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
}

.tui-img-vip {
  width: 56rpx;
  height: 24rpx;
  margin-left: 18rpx;
}

.tui-explain {
  width: 80%;
  font-size: 24rpx;
  font-weight: 400;
  color: #fff;
  opacity: 0.75;
  padding-top: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tui-btn-edit {
  flex-shrink: 0;
  padding-right: 22rpx;
}

.tui-header-btm {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  top: 280rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
}

.tui-btm-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tui-btm-num {
  font-size: 32rpx;
  font-weight: 600;
  position: relative;
}

.tui-btm-text {
  font-size: 24rpx;
  opacity: 0.85;
  padding-top: 4rpx;
}

.tui-content-box {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
  top: -72rpx;
  z-index: 10;
}

.tui-box {
  width: 100%;
  background: #fff;
  box-shadow: 0 3rpx 20rpx rgba(183, 183, 183, 0.1);
  border-radius: 10rpx;
  overflow: hidden;
}

.tui-order-box {
  height: 208rpx;
}

.tui-cell-header {
  width: 100%;
  height: 74rpx;
  padding: 0 26rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tui-cell-title {
  font-size: 30rpx;
  line-height: 30rpx;
  font-weight: 600;
  color: #333;
}

.tui-cell-sub {
  font-size: 26rpx;
  font-weight: 400;
  color: #999;
  padding-right: 28rpx;
}

.tui-order-list {
  width: 100%;
  height: 134rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

}

.tui-order-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tui-order-text,
.tui-tool-text {
  font-size: 26rpx;
  font-weight: 400;
  color: #666;
  padding-top: 4rpx;
}

.tui-tool-text {
  font-size: 24rpx;
}

.tui-order-icon {
  width: 56rpx;
  height: 56rpx;
  display: block;
}

.tui-assets-box {
  height: 178rpx;
  margin-top: 20rpx;
}

.tui-assets-list {
  height: 84rpx;
}

.tui-assets-num {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
}

.tui-assets-text {
  font-size: 24rpx;
  font-weight: 400;
  color: #666;
  padding-top: 6rpx;
}

.tui-tool-box {
  margin-top: 20rpx;
}

.tui-flex-wrap {
  flex-wrap: wrap;
  height: auto;
  padding-bottom: 30rpx;
}

.tui-tool-item {
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding-top: 30rpx;
}

.tui-tool-icon {
  width: 64rpx;
  height: 64rpx;
  display: block;
}

.tui-badge-icon {
  width: 66rpx;
  height: 30rpx;
  position: absolute;
  right: 0;
  transform: translateX(88%);
  top: -15rpx;
}

/*为你推荐*/
.tui-product-list {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  flex-wrap: wrap;
  box-sizing: border-box;
}

.tui-product-container {
  flex: 1;
  margin-right: 2%;
}

.tui-product-container:last-child {
  margin-right: 0;
}

.tui-pro-item {
  width: 100%;
  margin-bottom: 4%;
  background: #fff;
  box-sizing: border-box;
  border-radius: 12rpx;
  overflow: hidden;
}

.tui-pro-img {
  width: 100%;
  display: block;
}

.tui-pro-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 20rpx;
}

.tui-pro-tit {
  color: #2e2e2e;
  font-size: 26rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.tui-pro-price {
  padding-top: 18rpx;
}

.tui-sale-price {
  font-size: 34rpx;
  font-weight: 500;
  color: #e41f19;
}

.tui-factory-price {
  font-size: 24rpx;
  color: #a0a0a0;
  text-decoration: line-through;
  padding-left: 12rpx;
}

.tui-pro-pay {
  padding-top: 10rpx;
  font-size: 24rpx;
  color: #656565;
}

.icon{
  width: 48rpx;
  height: 48rpx;
}
.container{
  width: 100%;
  padding: 10rpx 30rpx 80rpx;
  box-sizing: border-box;
}
.tui-login__box{
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 3rpx 20rpx rgba(183, 183, 183, 0.1);
}
.phone_box{
  margin: 100rpx;
}
.tui-bg__img{
  width: 100%;
  height: 400rpx;
  display: block;
}

.tui-btn__box{
  width: 100%;
  padding:100rpx;
  padding: 24rpx 60rpx;
  box-sizing: border-box;
}
.tui-button{
  padding: 20rpx 0;
}
.signin{
  position: fixed;
  bottom: 100rpx;
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: #ac9157;
}
.company-info{
  position: fixed;
  bottom: 40rpx;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: #cccccc;
}
.tipinfo{
  font-size: 12px;
  color: #888;
}

.tui-login-box {
	padding: 20rpx 0;
}

.tui-line-cell {
	width: 100%;
	padding: 24rpx 30rpx;
	box-sizing: border-box;
	display: flex;
	align-items: center;
}

.tui-title {
	width: 180rpx;
	font-size: 28rpx;
}

.tui-title-city-text {
	width: 180rpx;
	height: 40rpx;
	display: block;
	line-height: 46rpx;
}

.tui-input {
	width: 500rpx;
}

.tui-input-city {
	flex: 1;
	height: 40rpx;
	font-size: 28rpx;
	padding-right: 30rpx;
}

.tui-phcolor {
	color: #ccc;
	font-size: 28rpx;
}
.tui-cell-title {
	font-size: 28rpx;
}

.tui-agreement{
	width: 100%;
	text-align: center;
	font-size: 24rpx;
  color: #999999;
  margin: 6rpx;
	padding-top: 12rpx;
}
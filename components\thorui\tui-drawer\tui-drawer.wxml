<view class="tui-drawer-class" catchtouchmove="stop">
	<view wx:if="{{mask}}" style="z-index:{{maskZIndex}}" class="tui-drawer-mask {{visible?'tui-drawer-mask_show':''}}" bindtap="handleMaskClick"></view>
	<view class="tui-drawer-container {{'tui-drawer-container_'+mode}} {{visible ? 'tui-drawer-'+mode+'__show' : ''}}" style="z-index:{{zIndex}};background-color:{{backgroundColor}}">
		<slot></slot>
	</view>
</view>